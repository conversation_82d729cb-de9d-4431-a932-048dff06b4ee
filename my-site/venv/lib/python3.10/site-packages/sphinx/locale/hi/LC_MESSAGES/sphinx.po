# Translations template for Sphinx.
# Copyright (C) 2024 ORGANIZATION
# This file is distributed under the same license as the Sphinx project.
# 
# Translators:
# <PERSON><PERSON> <a<PERSON><PERSON><PERSON>@gmail.com>, 2019
# P<PERSON>an<PERSON> <<EMAIL>>, 2015-2016
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2019
msgid ""
msgstr ""
"Project-Id-Version: Sphinx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2024-04-14 23:27+0000\n"
"PO-Revision-Date: 2013-04-02 08:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020\n"
"Language-Team: Hindi (http://app.transifex.com/sphinx-doc/sphinx-1/language/hi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.14.0\n"
"Language: hi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: sphinx/application.py:157
#, python-format
msgid "Cannot find source directory (%s)"
msgstr "स्रोत निर्देशिका (%s) नहीं मिली"

#: sphinx/application.py:161
#, python-format
msgid "Output directory (%s) is not a directory"
msgstr ""

#: sphinx/application.py:165
msgid "Source directory and destination directory cannot be identical"
msgstr "स्रोत निर्देशिका और गंतव्य निर्देशिका समरूप नहीं हो सकतीं"

#: sphinx/application.py:197
#, python-format
msgid "Running Sphinx v%s"
msgstr "स्फिंक्स %s संस्करण चल रहा है"

#: sphinx/application.py:219
#, python-format
msgid ""
"This project needs at least Sphinx v%s and therefore cannot be built with "
"this version."
msgstr "इस परियोजना में स्फिंक्स का कम से कम %s संस्करण चाहिए और इसलिए इस संस्करण से बनाना संभव नहीं है."

#: sphinx/application.py:235
msgid "making output directory"
msgstr "परिणाम निर्देशिका बनाई जा रही है"

#: sphinx/application.py:240 sphinx/registry.py:450
#, python-format
msgid "while setting up extension %s:"
msgstr "%s आयाम को स्थापित करते हुए:"

#: sphinx/application.py:246
msgid ""
"'setup' as currently defined in conf.py isn't a Python callable. Please "
"modify its definition to make it a callable function. This is needed for "
"conf.py to behave as a Sphinx extension."
msgstr "'स्थापना' को जैसा कि अभी कोन्फ़.पाई में परिभाषित किया गया है, पाइथन से निर्देशित नहीं है. कृपया इसकी परिभाषा में परिवर्तन करके इसे निर्देश योग्य कर्म बनाएं. कोन्फ़.पाई को स्फिंक्स के आयाम की तरह व्यवहार के लिए इसकी आवश्कयता है."

#: sphinx/application.py:277
#, python-format
msgid "loading translations [%s]... "
msgstr "[%s] अनुवाद पढ़ा जा रहा है..."

#: sphinx/application.py:294 sphinx/util/display.py:85
msgid "done"
msgstr "संपन्न"

#: sphinx/application.py:296
msgid "not available for built-in messages"
msgstr "अंतर्निर्मित संदेशों में उपलब्ध नहीं है"

#: sphinx/application.py:310
msgid "loading pickled environment"
msgstr "रक्षित स्थिति को लागू किया जा रहा है"

#: sphinx/application.py:318
#, python-format
msgid "failed: %s"
msgstr "असफल: %s"

#: sphinx/application.py:332
msgid "No builder selected, using default: html"
msgstr "किसी निर्माता को नहीं चुना गया, मानक उपयोग: एच्.टी.ऍम.एल."

#: sphinx/application.py:365
msgid "succeeded"
msgstr "सफल हुआ"

#: sphinx/application.py:366
msgid "finished with problems"
msgstr "समस्याओं के साथ समाप्त हुआ"

#: sphinx/application.py:370
#, python-format
msgid "build %s, %s warning (with warnings treated as errors)."
msgstr "%s निर्माण, चेतावनी %s (चेतावनी को गलती माने)| "

#: sphinx/application.py:372
#, python-format
msgid "build %s, %s warnings (with warnings treated as errors)."
msgstr ""

#: sphinx/application.py:375
#, python-format
msgid "build %s, %s warning."
msgstr "%s सम्पूर्ण, %s चेतावनी."

#: sphinx/application.py:377
#, python-format
msgid "build %s, %s warnings."
msgstr ""

#: sphinx/application.py:381
#, python-format
msgid "build %s."
msgstr "%s निर्मित."

#: sphinx/application.py:610
#, python-format
msgid "node class %r is already registered, its visitors will be overridden"
msgstr "निर्देशक कक्षा #node class# %r पहले से पंजीकृत है, इसके अभ्यागत निरस्त हो जाएंगे "

#: sphinx/application.py:689
#, python-format
msgid "directive %r is already registered, it will be overridden"
msgstr "निर्देश %r पहले से पंजीकृत है, यह निरस्त हो जाएगा"

#: sphinx/application.py:711 sphinx/application.py:733
#, python-format
msgid "role %r is already registered, it will be overridden"
msgstr "भूमिका %r पहले से पंजीकृत है, यह निरस्त हो जाएगी"

#: sphinx/application.py:1282
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel reading, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr "%s आयाम यह घोषित नहीं करता कि यह समानांतर पाठन के लिए सुरक्षित है. यह मानते हुए की ऐसा नहीं है - कृपया आयाम के लेखक को जांच करने और स्पष्ट व्यक्त करने के लिए कहें."

#: sphinx/application.py:1286
#, python-format
msgid "the %s extension is not safe for parallel reading"
msgstr "समानांतर पठन के लिए यह %s विस्तार अथवा आयाम सुरक्षित नहीं है | "

#: sphinx/application.py:1289
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel writing, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr "%s आयाम यह घोषित नहीं करता कि यह समानांतर लेखन के लिए सुरक्षित है. यह मानते हुए की ऐसा नहीं है - कृपया आयाम के लेखक को जांच करने और स्पष्ट व्यक्त करने के लिए कहें."

#: sphinx/application.py:1293
#, python-format
msgid "the %s extension is not safe for parallel writing"
msgstr "समानांतर लेखन के लिए  %s विस्तार अथवा आयाम सुरक्षित नहीं है | "

#: sphinx/application.py:1301 sphinx/application.py:1305
#, python-format
msgid "doing serial %s"
msgstr "%s पर काम कर रहे हैं"

#: sphinx/config.py:309
#, python-format
msgid "config directory doesn't contain a conf.py file (%s)"
msgstr "विन्यास निर्देशिका में कोन्फ़.पाय #conf.py# फाइल (%s) नहीं है "

#: sphinx/config.py:318
msgid ""
"Invalid configuration value found: 'language = None'. Update your "
"configuration to a valid language code. Falling back to 'en' (English)."
msgstr ""

#: sphinx/config.py:341
#, python-format
msgid ""
"cannot override dictionary config setting %r, ignoring (use %r to set "
"individual elements)"
msgstr "शब्दकोष विन्यास मान %r की उल्लंघन नहीं किया जा सकता, अनदेखा किया गया (प्रत्येक अवयव का मान रखने के लिए %r का उपयोग करें)"

#: sphinx/config.py:350
#, python-format
msgid "invalid number %r for config value %r, ignoring"
msgstr "विन्यास मान %r के लिए अमान्य संख्या %r, अनदेखा किया गया"

#: sphinx/config.py:355
#, python-format
msgid "cannot override config setting %r with unsupported type, ignoring"
msgstr "असमर्थित प्रकार के साथ विन्यास मान %r का उल्लंघन नहीं किया जा सकता, अनदेखा किया गया"

#: sphinx/config.py:378
#, python-format
msgid "unknown config value %r in override, ignoring"
msgstr "आरोहण में अज्ञात विन्यास मान %r, अनदेखा किया गया"

#: sphinx/config.py:418
#, python-format
msgid "No such config value: %r"
msgstr ""

#: sphinx/config.py:440
#, python-format
msgid "Config value %r already present"
msgstr "विन्यास मान %r पहले से विद्यमान है"

#: sphinx/config.py:473
#, python-format
msgid "cannot cache unpickable configuration value: %r"
msgstr ""

#: sphinx/config.py:509
#, python-format
msgid "There is a syntax error in your configuration file: %s\n"
msgstr "आपकी विन्यास फाइल में रचनाक्रम की त्रुटि है: %s\n"

#: sphinx/config.py:512
msgid ""
"The configuration file (or one of the modules it imports) called sys.exit()"
msgstr "विन्यास फाइल (अथवा इसके द्वारा आयातित प्रभागों) द्वारा sys.exit() का आह्वान किया गया"

#: sphinx/config.py:519
#, python-format
msgid ""
"There is a programmable error in your configuration file:\n"
"\n"
"%s"
msgstr "विन्यास फाइल में प्रोग्राम के योग्य त्रुटि है:\n\n%s"

#: sphinx/config.py:540
#, python-format
msgid "Failed to convert %r to a set or tuple"
msgstr ""

#: sphinx/config.py:565
#, python-format
msgid ""
"The config value `source_suffix' expects a string, list of strings, or "
"dictionary. But `%r' is given."
msgstr "विन्यास मान `source_suffix' में अक्षर-समूह, अक्षर-समूहों की सूची, अथवा कोष की अनुमति है. लेकिन `%r' दिया गया है."

#: sphinx/config.py:585
#, python-format
msgid "Section %s"
msgstr "भाग %s"

#: sphinx/config.py:586
#, python-format
msgid "Fig. %s"
msgstr "चित्र %s"

#: sphinx/config.py:587
#, python-format
msgid "Table %s"
msgstr "सारणी %s"

#: sphinx/config.py:588
#, python-format
msgid "Listing %s"
msgstr "सूची %s"

#: sphinx/config.py:663
msgid ""
"The config value `{name}` has to be a one of {candidates}, but `{current}` "
"is given."
msgstr "`{name}` विन्यास मान, {candidates} में से एक होना चाहिए, परन्तु `{current}` दिया गया है."

#: sphinx/config.py:687
msgid ""
"The config value `{name}' has type `{current.__name__}'; expected "
"{permitted}."
msgstr "विन्यास मान `{name}' का प्रकार `{current.__name__}' है; अपेक्षित {permitted}."

#: sphinx/config.py:700
msgid ""
"The config value `{name}' has type `{current.__name__}', defaults to "
"`{default.__name__}'."
msgstr "विन्यास मान `{name}' का प्रकार `{current.__name__}' है; मानक `{default.__name__}' का प्रयोग किया गया."

#: sphinx/config.py:711
#, python-format
msgid "primary_domain %r not found, ignored."
msgstr "primary_domain %r नहीं मिला, अनदेखा किया गया."

#: sphinx/config.py:723
msgid ""
"Since v2.0, Sphinx uses \"index\" as root_doc by default. Please add "
"\"root_doc = 'contents'\" to your conf.py."
msgstr ""

#: sphinx/events.py:64
#, python-format
msgid "Event %r already present"
msgstr "%r घटना पहले से विद्यमान है"

#: sphinx/events.py:70
#, python-format
msgid "Unknown event name: %s"
msgstr "अज्ञात घटना नाम: %s"

#: sphinx/events.py:109
#, python-format
msgid "Handler %r for event %r threw an exception"
msgstr ""

#: sphinx/extension.py:55
#, python-format
msgid ""
"The %s extension is required by needs_extensions settings, but it is not "
"loaded."
msgstr "आयाम %s की needs_extensions मान में आवश्कता है, पर यह नहीं चढ़ाया गया है."

#: sphinx/extension.py:76
#, python-format
msgid ""
"This project needs the extension %s at least in version %s and therefore "
"cannot be built with the loaded version (%s)."
msgstr "इस परियोजना में आयाम %s का कम से कम %s संस्करण चाहिए इसलिए उपलब्ध संस्करण (%s) से बनाना संभव नहीं है."

#: sphinx/highlighting.py:155
#, python-format
msgid "Pygments lexer name %r is not known"
msgstr "पिगमेंटस लेक्सर नाम %r अज्ञात है"

#: sphinx/highlighting.py:189
#, python-format
msgid ""
"Lexing literal_block %r as \"%s\" resulted in an error at token: %r. "
"Retrying in relaxed mode."
msgstr ""

#: sphinx/project.py:66
#, python-format
msgid ""
"multiple files found for the document \"%s\": %r\n"
"Use %r for the build."
msgstr ""

#: sphinx/project.py:81
#, python-format
msgid "Ignored unreadable document %r."
msgstr ""

#: sphinx/registry.py:142
#, python-format
msgid "Builder class %s has no \"name\" attribute"
msgstr "निर्माण वर्ग %s का कोई \"नाम\" भाव नहीं है"

#: sphinx/registry.py:144
#, python-format
msgid "Builder %r already exists (in module %s)"
msgstr "निर्माता %r पहले से (%s प्रभाग में) उपलब्ध है"

#: sphinx/registry.py:157
#, python-format
msgid "Builder name %s not registered or available through entry point"
msgstr "निर्माता नाम %s पंजीकृत नहीं है अथवा प्रवेश स्थान पर उपलब्ध नहीं है."

#: sphinx/registry.py:164
#, python-format
msgid "Builder name %s not registered"
msgstr "निर्माता नाम %s पंजीकृत नहीं है"

#: sphinx/registry.py:171
#, python-format
msgid "domain %s already registered"
msgstr "अधिकारक्षेत्र %s पहले से पंजीकृत है"

#: sphinx/registry.py:194 sphinx/registry.py:207 sphinx/registry.py:218
#, python-format
msgid "domain %s not yet registered"
msgstr "अधिकारक्षेत्र %s अभी पंजीकृत नहीं है"

#: sphinx/registry.py:198
#, python-format
msgid "The %r directive is already registered to domain %s"
msgstr "%r निर्देश पहले से अधिकार-क्षेत्र %s में पंजीकृत है, "

#: sphinx/registry.py:210
#, python-format
msgid "The %r role is already registered to domain %s"
msgstr "%r भूमिका पहले से अधिकार-क्षेत्र %s में पंजीकृत है, "

#: sphinx/registry.py:221
#, python-format
msgid "The %r index is already registered to domain %s"
msgstr "%r अनुक्रमणिका  पहले से अधिकार-क्षेत्र %s में पंजीकृत है"

#: sphinx/registry.py:252
#, python-format
msgid "The %r object_type is already registered"
msgstr "%r object_type पहले से पंजीकृत है"

#: sphinx/registry.py:278
#, python-format
msgid "The %r crossref_type is already registered"
msgstr "%r crossref_type पहले से पंजीकृत है"

#: sphinx/registry.py:285
#, python-format
msgid "source_suffix %r is already registered"
msgstr "source_suffix %r पहले से पंजीकृत है"

#: sphinx/registry.py:294
#, python-format
msgid "source_parser for %r is already registered"
msgstr "%r का source_parser पहले से पंजीकृत है"

#: sphinx/registry.py:302
#, python-format
msgid "Source parser for %s not registered"
msgstr "%s का स्रोत व्याख्याता पंजीकृत नहीं है"

#: sphinx/registry.py:318
#, python-format
msgid "Translator for %r already exists"
msgstr "%r के लिए अनुवादक पहले से विद्यमान है"

#: sphinx/registry.py:334
#, python-format
msgid "kwargs for add_node() must be a (visit, depart) function tuple: %r=%r"
msgstr "add_node() के kwargs एक (visit, depart) फंक्शन टपल #function tuple# होने चाहिए: %r=%r"

#: sphinx/registry.py:417
#, python-format
msgid "enumerable_node %r already registered"
msgstr "enumerable_node %r पहले से पंजीकृत है"

#: sphinx/registry.py:429
#, python-format
msgid "math renderer %s is already registered"
msgstr ""

#: sphinx/registry.py:444
#, python-format
msgid ""
"the extension %r was already merged with Sphinx since version %s; this "
"extension is ignored."
msgstr "%r आयाम को %sसंस्करण से स्फिंक्स में सम्मिलित किया जा चुका है; आयाम की उपेक्षा की गयी."

#: sphinx/registry.py:455
msgid "Original exception:\n"
msgstr "मौलिक अपवाद:\n"

#: sphinx/registry.py:456
#, python-format
msgid "Could not import extension %s"
msgstr "%s आयाम का आयात नहीं किया जा सका"

#: sphinx/registry.py:461
#, python-format
msgid ""
"extension %r has no setup() function; is it really a Sphinx extension "
"module?"
msgstr "आयाम %r में कोई सेटअप #setup()# कारक नहीं है; क्या यह वास्तव में स्फिंक्स का परिवर्धक प्रभाग है?"

#: sphinx/registry.py:470
#, python-format
msgid ""
"The %s extension used by this project needs at least Sphinx v%s; it "
"therefore cannot be built with this version."
msgstr "इस परियोजना में प्रयुक्त %s परिवर्धक को स्फिंक्स का कम से कम %s संस्करण चाहिए; इसलिए इस संस्करण से बनाना संभव नहीं है."

#: sphinx/registry.py:478
#, python-format
msgid ""
"extension %r returned an unsupported object from its setup() function; it "
"should return None or a metadata dictionary"
msgstr "परिवर्धक %r के सेटअप() कर्म से एक असहाय वस्तु वापस मिली है; इसको 'कुछ नहीं' अथवा मेटाडाटा कोश भेजना चाहिए था"

#: sphinx/roles.py:201
#, python-format
msgid "Python Enhancement Proposals; PEP %s"
msgstr "पाइथन अभिवृद्धि प्रस्ताव; पी.ई.पी. %s"

#: sphinx/roles.py:222
#, python-format
msgid "invalid PEP number %s"
msgstr ""

#: sphinx/roles.py:257
#, python-format
msgid "invalid RFC number %s"
msgstr ""

#: sphinx/theming.py:125
#, python-format
msgid "setting %s.%s occurs in none of the searched theme configs"
msgstr "विन्यास मान %s.%s खोजे गए किसी भी रूप विन्यास में नहीं दिखा"

#: sphinx/theming.py:140
#, python-format
msgid "unsupported theme option %r given"
msgstr "विन्यास का असमर्थित रूप विकल्प %r दिया गया"

#: sphinx/theming.py:206
#, python-format
msgid "file %r on theme path is not a valid zipfile or contains no theme"
msgstr "रुपविन्यास के पथ में फाइल %r कोई प्रमाणिक ज़िप फाइल नहीं है या इसमें कोई रुपविन्यास नहीं सहेजा गया है"

#: sphinx/theming.py:226
#, python-format
msgid "no theme named %r found (missing theme.toml?)"
msgstr ""

#: sphinx/theming.py:259
#, python-format
msgid "The %r theme has circular inheritance"
msgstr ""

#: sphinx/theming.py:262
#, python-format
msgid ""
"The %r theme inherits from %r, which is not a loaded theme. Loaded themes "
"are: %s"
msgstr ""

#: sphinx/theming.py:269
#, python-format
msgid "The %r theme has too many ancestors"
msgstr ""

#: sphinx/theming.py:295
#, python-format
msgid "no theme configuration file found in %r"
msgstr ""

#: sphinx/theming.py:323 sphinx/theming.py:374
#, python-format
msgid "theme %r doesn't have the \"theme\" table"
msgstr ""

#: sphinx/theming.py:327
#, python-format
msgid "The %r theme \"[theme]\" table is not a table"
msgstr ""

#: sphinx/theming.py:331 sphinx/theming.py:377
#, python-format
msgid "The %r theme must define the \"theme.inherit\" setting"
msgstr ""

#: sphinx/theming.py:335
#, python-format
msgid "The %r theme \"[options]\" table is not a table"
msgstr ""

#: sphinx/theming.py:353
#, python-format
msgid "The \"theme.pygments_style\" setting must be a table. Hint: \"%s\""
msgstr ""

#: sphinx/builders/__init__.py:183
#, python-format
msgid "a suitable image for %s builder not found: %s (%s)"
msgstr "%s निर्माता के लिए योग्य चित्र नहीं मिला: %s.(%s)"

#: sphinx/builders/__init__.py:187
#, python-format
msgid "a suitable image for %s builder not found: %s"
msgstr "%s निर्माता के लिए योग्य चित्र नहीं मिला: %s"

#: sphinx/builders/__init__.py:207
msgid "building [mo]: "
msgstr "निर्माणाधीन [mo]: "

#: sphinx/builders/__init__.py:208 sphinx/builders/__init__.py:574
#: sphinx/builders/__init__.py:601
msgid "writing output... "
msgstr "परिणाम लिखा जा रहा है..."

#: sphinx/builders/__init__.py:217
#, python-format
msgid "all of %d po files"
msgstr "सभी %d पी.ओ. फाइलें"

#: sphinx/builders/__init__.py:235
#, python-format
msgid "targets for %d po files that are specified"
msgstr "निर्दिष्ट %d पी.ओ. फाइलों के लक्ष्य"

#: sphinx/builders/__init__.py:243
#, python-format
msgid "targets for %d po files that are out of date"
msgstr "%d पी.ओ. फाइलों के लक्ष्य कालातीत है"

#: sphinx/builders/__init__.py:252
msgid "all source files"
msgstr "सभी स्रोत फाइलें"

#: sphinx/builders/__init__.py:262
#, python-format
msgid "file %r given on command line does not exist, "
msgstr ""

#: sphinx/builders/__init__.py:267
#, python-format
msgid ""
"file %r given on command line is not under the source directory, ignoring"
msgstr "आदेश स्थान में दी गयी फाइल %r स्रोत निर्देशिका में नहीं है, उपेक्षा की जा रही है"

#: sphinx/builders/__init__.py:273
#, python-format
msgid "file %r given on command line is not a valid document, ignoring"
msgstr ""

#: sphinx/builders/__init__.py:282
#, python-format
msgid "%d source files given on command line"
msgstr "%d स्रोत फाइलें आदेश स्थान में दी "

#: sphinx/builders/__init__.py:294
#, python-format
msgid "targets for %d source files that are out of date"
msgstr "%d  फाइलों के लक्ष्य कालातीत है"

#: sphinx/builders/__init__.py:309 sphinx/builders/gettext.py:243
#, python-format
msgid "building [%s]: "
msgstr "निर्माणाधीन [%s]: "

#: sphinx/builders/__init__.py:316
msgid "looking for now-outdated files... "
msgstr "अप्रचलित फाइलों को चिन्हित किया जा रहा है..."

#: sphinx/builders/__init__.py:320
#, python-format
msgid "%d found"
msgstr "%d मिला"

#: sphinx/builders/__init__.py:322
msgid "none found"
msgstr "एक भी नहीं मिला"

#: sphinx/builders/__init__.py:327
msgid "pickling environment"
msgstr "स्थिति को परिरक्षित किया जा रहा है"

#: sphinx/builders/__init__.py:333
msgid "checking consistency"
msgstr "संगतता की जांच की जा रही है"

#: sphinx/builders/__init__.py:337
msgid "no targets are out of date."
msgstr "कोई प्रयोजन कालातीत नहीं है"

#: sphinx/builders/__init__.py:376
msgid "updating environment: "
msgstr "स्थिति का नवीनीकरण किया जा रहा है"

#: sphinx/builders/__init__.py:397
#, python-format
msgid "%s added, %s changed, %s removed"
msgstr "%s जोड़ा गया, %s बदला गया, %s हटाया गया"

#: sphinx/builders/__init__.py:435 sphinx/builders/__init__.py:447
msgid "reading sources... "
msgstr "स्रोतों को पढ़ा जा रहा है..."

#: sphinx/builders/__init__.py:549
#, python-format
msgid "docnames to write: %s"
msgstr "लेखन के लिए शेष लेखपत्र: %s"

#: sphinx/builders/__init__.py:558 sphinx/builders/singlehtml.py:157
msgid "preparing documents"
msgstr "लेखपत्र बनाए जा रहे हैं"

#: sphinx/builders/__init__.py:561
msgid "copying assets"
msgstr ""

#: sphinx/builders/_epub_base.py:215
#, python-format
msgid "duplicated ToC entry found: %s"
msgstr "विषय-सूची प्रविष्टि की प्रतिलिपि पायी गई: %s"

#: sphinx/builders/_epub_base.py:404 sphinx/builders/html/__init__.py:758
#: sphinx/builders/latex/__init__.py:432 sphinx/builders/texinfo.py:187
msgid "copying images... "
msgstr "चित्रों की प्रतिलिपि बनाई जा रही है..."

#: sphinx/builders/_epub_base.py:411
#, python-format
msgid "cannot read image file %r: copying it instead"
msgstr "चित्रलेख फाइल %r नहीं पढ़ा जा सका: इसकी प्रतिलिपि बनाई जा रही है"

#: sphinx/builders/_epub_base.py:417 sphinx/builders/html/__init__.py:766
#: sphinx/builders/latex/__init__.py:440 sphinx/builders/texinfo.py:197
#, python-format
msgid "cannot copy image file %r: %s"
msgstr "चित्रलेख फाइल %r की प्रतिलिपि नहीं की जा सकी:%s"

#: sphinx/builders/_epub_base.py:434
#, python-format
msgid "cannot write image file %r: %s"
msgstr "चित्रलेख फाइल %r नहीं लिखा जा सका:%s"

#: sphinx/builders/_epub_base.py:444
msgid "Pillow not found - copying image files"
msgstr "पिलो नहीं मिला - चित्र फाइलों की प्रतिलिपि बनाई जा रही है"

#: sphinx/builders/_epub_base.py:470
msgid "writing mimetype file..."
msgstr ""

#: sphinx/builders/_epub_base.py:475
msgid "writing META-INF/container.xml file..."
msgstr ""

#: sphinx/builders/_epub_base.py:508
msgid "writing content.opf file..."
msgstr ""

#: sphinx/builders/_epub_base.py:539
#, python-format
msgid "unknown mimetype for %s, ignoring"
msgstr "%s के लिए अज्ञात लेख प्रकार, छोड़ा गया"

#: sphinx/builders/_epub_base.py:686
msgid "writing toc.ncx file..."
msgstr ""

#: sphinx/builders/_epub_base.py:711
#, python-format
msgid "writing %s file..."
msgstr "%s फाइल को लिखा जा रहा है..."

#: sphinx/builders/changes.py:32
#, python-format
msgid "The overview file is in %(outdir)s."
msgstr "संक्षिप्त विवरण फाइल %(outdir)s में है."

#: sphinx/builders/changes.py:59
#, python-format
msgid "no changes in version %s."
msgstr "%s संस्करण में कोई परिवर्तन नहीं हैं."

#: sphinx/builders/changes.py:61
msgid "writing summary file..."
msgstr "सार फाइल को लिखा जा रहा है..."

#: sphinx/builders/changes.py:76
msgid "Builtins"
msgstr "अंतर्निर्मित"

#: sphinx/builders/changes.py:78
msgid "Module level"
msgstr "प्रभाग स्तर"

#: sphinx/builders/changes.py:123
msgid "copying source files..."
msgstr "स्रोत फाइलों की प्रतिलिपि बनाई जा रही है..."

#: sphinx/builders/changes.py:130
#, python-format
msgid "could not read %r for changelog creation"
msgstr "परिवर्तन सूची बनाने के लिए %r को नहीं पढ़ा जा सका"

#: sphinx/builders/dummy.py:19
msgid "The dummy builder generates no files."
msgstr "मूक निर्माता से किसी फाइलों की उत्पत्ति नहीं होती."

#: sphinx/builders/epub3.py:81
#, python-format
msgid "The ePub file is in %(outdir)s."
msgstr "ई-पब फाइल %(outdir)s में है."

#: sphinx/builders/epub3.py:185
msgid "writing nav.xhtml file..."
msgstr ""

#: sphinx/builders/epub3.py:211
msgid "conf value \"epub_language\" (or \"language\") should not be empty for EPUB3"
msgstr "ई-पब3 के लिए विन्यास मान \"epub_language\" (अथवा \"language\") खाली नहीं होना चाहिए"

#: sphinx/builders/epub3.py:215
msgid "conf value \"epub_uid\" should be XML NAME for EPUB3"
msgstr "ई-पब3 के लिए विन्यास मान \"epub_uid\" एक्स.एम्.एल. नाम होना चाहिए"

#: sphinx/builders/epub3.py:218
msgid "conf value \"epub_title\" (or \"html_title\") should not be empty for EPUB3"
msgstr "ई-पब3 के लिए विन्यास मान \"epub_title\" (अथवा \"html_title\") खाली नहीं होना चाहिए"

#: sphinx/builders/epub3.py:222
msgid "conf value \"epub_author\" should not be empty for EPUB3"
msgstr "ई-पब3 के लिए विन्यास मान \"epub_author\" खाली नहीं होना चाहिए"

#: sphinx/builders/epub3.py:225
msgid "conf value \"epub_contributor\" should not be empty for EPUB3"
msgstr "ई-पब3 के लिए विन्यास मान \"epub_contributor\" खाली नहीं होना चाहिए"

#: sphinx/builders/epub3.py:228
msgid "conf value \"epub_description\" should not be empty for EPUB3"
msgstr "ई-पब3 के लिए विन्यास मान \"epub_description\" खाली नहीं होना चाहिए"

#: sphinx/builders/epub3.py:231
msgid "conf value \"epub_publisher\" should not be empty for EPUB3"
msgstr "ई-पब3 के लिए विन्यास मान \"epub_publisher\" खाली नहीं होना चाहिए"

#: sphinx/builders/epub3.py:234
msgid "conf value \"epub_copyright\" (or \"copyright\")should not be empty for EPUB3"
msgstr "ई-पब3 के लिए विन्यास मान \"epub_copyright\" (अथवा \"copyright\") खाली नहीं होना चाहिए"

#: sphinx/builders/epub3.py:238
msgid "conf value \"epub_identifier\" should not be empty for EPUB3"
msgstr "ई-पब3 के लिए विन्यास मान \"epub_identifier\" खाली नहीं होना चाहिए"

#: sphinx/builders/epub3.py:241
msgid "conf value \"version\" should not be empty for EPUB3"
msgstr "ई-पब3 के लिए विन्यास मान \"version\" खाली नहीं होना चाहिए"

#: sphinx/builders/epub3.py:255 sphinx/builders/html/__init__.py:1187
#, python-format
msgid "invalid css_file: %r, ignored"
msgstr "अमान्य css_file: %r, उपेक्षित"

#: sphinx/builders/gettext.py:222
#, python-format
msgid "The message catalogs are in %(outdir)s."
msgstr "सन्देश सूचीपत्र %(outdir)s में हैं."

#: sphinx/builders/gettext.py:244
#, python-format
msgid "targets for %d template files"
msgstr "%d नमूना फाइलों के लक्ष्य"

#: sphinx/builders/gettext.py:248
msgid "reading templates... "
msgstr "नमूनों को पढ़ा जा रहा है..."

#: sphinx/builders/gettext.py:282
msgid "writing message catalogs... "
msgstr "सन्देश सूचीपत्रों को लिखा जा रहा है..."

#: sphinx/builders/linkcheck.py:59
#, python-format
msgid "Look for any errors in the above output or in %(outdir)s/output.txt"
msgstr "उपरोक्त परिणाम में अथवा %(outdir)s /output.txt में त्रुटियाँ ढूँढने का प्रयास "

#: sphinx/builders/linkcheck.py:137
#, python-format
msgid "broken link: %s (%s)"
msgstr "खंडित कड़ी: %s (%s)"

#: sphinx/builders/linkcheck.py:660
#, python-format
msgid "Failed to compile regex in linkcheck_allowed_redirects: %r %s"
msgstr ""

#: sphinx/builders/manpage.py:37
#, python-format
msgid "The manual pages are in %(outdir)s."
msgstr "पुस्तिका पृष्ठ %(outdir)sमें हैं."

#: sphinx/builders/manpage.py:44
msgid "no \"man_pages\" config value found; no manual pages will be written"
msgstr "कोई \"man_pages\" विन्यास मान नहीं मिला; कोई नियमावली पृष्ठ नहीं लिखे जाएंगे"

#: sphinx/builders/latex/__init__.py:314 sphinx/builders/manpage.py:53
#: sphinx/builders/singlehtml.py:165 sphinx/builders/texinfo.py:112
msgid "writing"
msgstr "लिखा जा रहा है"

#: sphinx/builders/manpage.py:68
#, python-format
msgid "\"man_pages\" config value references unknown document %s"
msgstr "\"man_pages\" विन्यास मान अज्ञात लेखपत्र %s का सन्दर्भ है"

#: sphinx/builders/singlehtml.py:34
#, python-format
msgid "The HTML page is in %(outdir)s."
msgstr "एच.टी.एम्.एल. पृष्ठ %(outdir)sमें है."

#: sphinx/builders/singlehtml.py:160
msgid "assembling single document"
msgstr "एकल लेखपत्र संकलन किया जा रहा है"

#: sphinx/builders/singlehtml.py:178
msgid "writing additional files"
msgstr "अतिरिक्त फाइलों को लिखा जा रहा है"

#: sphinx/builders/texinfo.py:48
#, python-format
msgid "The Texinfo files are in %(outdir)s."
msgstr "टेक्सइन्फो पृष्ठ %(outdir)sमें हैं."

#: sphinx/builders/texinfo.py:50
msgid ""
"\n"
"Run 'make' in that directory to run these through makeinfo\n"
"(use 'make info' here to do that automatically)."
msgstr "\nइन्हें मेकइन्फो से चलाने के लिए उस निर्देशिका में 'मेक' आदेश चलायें\n(ऐसा स्वचालित रूप से करने के लिए यहाँ 'मेक इन्फो' आदेश का उपयोग करें)"

#: sphinx/builders/texinfo.py:77
msgid "no \"texinfo_documents\" config value found; no documents will be written"
msgstr "कोई \"texinfo_documents\" विन्यास मान नहीं मिला; कोई लेखपत्र नहीं लिखे जाएंगे"

#: sphinx/builders/texinfo.py:85
#, python-format
msgid "\"texinfo_documents\" config value references unknown document %s"
msgstr "\"texinfo_documents\" विन्यास मान अज्ञात लेखपत्र %s का सन्दर्भ है"

#: sphinx/builders/latex/__init__.py:296 sphinx/builders/texinfo.py:108
#, python-format
msgid "processing %s"
msgstr "%s की प्रक्रिया जारी"

#: sphinx/builders/latex/__init__.py:369 sphinx/builders/texinfo.py:161
msgid "resolving references..."
msgstr "सन्दर्भों का विश्लेषण किया जा रहा है..."

#: sphinx/builders/latex/__init__.py:380 sphinx/builders/texinfo.py:171
msgid " (in "
msgstr " (में"

#: sphinx/builders/texinfo.py:202
msgid "copying Texinfo support files"
msgstr "टेक्सइन्फो सहायक फाइलों की प्रतिलिपि की जा रही है..."

#: sphinx/builders/texinfo.py:206
#, python-format
msgid "error writing file Makefile: %s"
msgstr "मेकफाइल लिखने में त्रुटि: %s"

#: sphinx/builders/text.py:30
#, python-format
msgid "The text files are in %(outdir)s."
msgstr "पाठ फाइल %(outdir)s में हैं."

#: sphinx/builders/html/__init__.py:1138 sphinx/builders/text.py:77
#: sphinx/builders/xml.py:96
#, python-format
msgid "error writing file %s: %s"
msgstr "%s फाइल लिखने में व्यवधान: %s"

#: sphinx/builders/xml.py:36
#, python-format
msgid "The XML files are in %(outdir)s."
msgstr "एक्स.एम्.एल. लेखपत्र %(outdir)s में हैं."

#: sphinx/builders/xml.py:109
#, python-format
msgid "The pseudo-XML files are in %(outdir)s."
msgstr "छद्म-एक्स.एम्.एल. लेखपत्र %(outdir)s में हैं."

#: sphinx/builders/html/__init__.py:130
#, python-format
msgid "build info file is broken: %r"
msgstr "निर्माण सूचनापत्र फाइल खंडित है: %r"

#: sphinx/builders/html/__init__.py:168
#, python-format
msgid "The HTML pages are in %(outdir)s."
msgstr "एच.टी.एम्.एल. पृष्ठ %(outdir)sमें हैं."

#: sphinx/builders/html/__init__.py:394
#, python-format
msgid "Failed to read build info file: %r"
msgstr "निर्माण सूचनापत्र फाइल को नहीं पढ़ा जा सका: %r"

#: sphinx/builders/html/__init__.py:487 sphinx/builders/latex/__init__.py:189
#: sphinx/transforms/__init__.py:119 sphinx/writers/manpage.py:101
#: sphinx/writers/texinfo.py:227
#, python-format
msgid "%b %d, %Y"
msgstr "%b %d, %Y"

#: sphinx/builders/html/__init__.py:506 sphinx/themes/basic/defindex.html:30
msgid "General Index"
msgstr "सामान्य अनुक्रमाणिका"

#: sphinx/builders/html/__init__.py:506
msgid "index"
msgstr "अनुक्रमणिका"

#: sphinx/builders/html/__init__.py:579
msgid "next"
msgstr "आगामी"

#: sphinx/builders/html/__init__.py:588
msgid "previous"
msgstr "पूर्ववर्ती"

#: sphinx/builders/html/__init__.py:684
msgid "generating indices"
msgstr "अनुक्रमाणिका निर्मित की जा रही है"

#: sphinx/builders/html/__init__.py:699
msgid "writing additional pages"
msgstr "अतिरिक्त पृष्ठ लिखे जा रहे हैं"

#: sphinx/builders/html/__init__.py:776
msgid "copying downloadable files... "
msgstr "उतारी गई फाइलों की प्रतिलिपि बनाई जा रही है..."

#: sphinx/builders/html/__init__.py:784
#, python-format
msgid "cannot copy downloadable file %r: %s"
msgstr "उतारी गई फाइलों %r की प्रतिलिपि नहीं की जा सकी: %s"

#: sphinx/builders/html/__init__.py:817 sphinx/builders/html/__init__.py:829
#, python-format
msgid "Failed to copy a file in html_static_file: %s: %r"
msgstr ""

#: sphinx/builders/html/__init__.py:850
msgid "copying static files"
msgstr ""

#: sphinx/builders/html/__init__.py:866
#, python-format
msgid "cannot copy static file %r"
msgstr "स्थैतिक फाइल %r की प्रतिलिपि नहीं की जा सकी"

#: sphinx/builders/html/__init__.py:871
msgid "copying extra files"
msgstr "अतिरिक्त फाइलों की प्रतिलिपियां बनाये जा रहे है| "

#: sphinx/builders/html/__init__.py:877
#, python-format
msgid "cannot copy extra file %r"
msgstr "अतिरिक्त फाइल %r की प्रतिलिपि नहीं की जा सकी"

#: sphinx/builders/html/__init__.py:884
#, python-format
msgid "Failed to write build info file: %r"
msgstr "निर्माण फाइल को नहीं लिखा जा सका: %r"

#: sphinx/builders/html/__init__.py:933
msgid ""
"search index couldn't be loaded, but not all documents will be built: the "
"index will be incomplete."
msgstr "खोज अनुक्रमाणिका नहीं चढाई जा सकी, लेकिन सभी लेखपत्र नहीं बनाए जाएंगे: अनुक्रमणिका अपूर्ण रहेगी."

#: sphinx/builders/html/__init__.py:978
#, python-format
msgid "page %s matches two patterns in html_sidebars: %r and %r"
msgstr "पृष्ठ %s html_sidebars में दो आकृतियों से मिलता है: %r  %r"

#: sphinx/builders/html/__init__.py:1121
#, python-format
msgid ""
"a Unicode error occurred when rendering the page %s. Please make sure all "
"config values that contain non-ASCII content are Unicode strings."
msgstr "पृष्ठ %s की प्रस्तुति करते समय यूनिकोड त्रुटि हुई. कृपया यह सुनिश्चित कर लें कि सभी नॉन-असकी #non-ASCII# विहित विन्यास मान यूनिकोड अक्षरों में हैं."

#: sphinx/builders/html/__init__.py:1126
#, python-format
msgid ""
"An error happened in rendering the page %s.\n"
"Reason: %r"
msgstr "पृष्ठ %s की प्रस्तुति करते समय एक त्रुटि हुई.\nकारण: %r"

#: sphinx/builders/html/__init__.py:1154
msgid "dumping object inventory"
msgstr "विषयवस्तुओं का भंडार बनाया जा रहा है"

#: sphinx/builders/html/__init__.py:1162
#, python-format
msgid "dumping search index in %s"
msgstr "%s में खोज अनुक्रमाणिका भंडार बनाया जा रहा है"

#: sphinx/builders/html/__init__.py:1210
#, python-format
msgid "invalid js_file: %r, ignored"
msgstr "अमान्य js_file: %r, उपेक्षित"

#: sphinx/builders/html/__init__.py:1238
msgid "Many math_renderers are registered. But no math_renderer is selected."
msgstr "कई math_renderers पंजीकृत हैं. लेकिन कोई math_renderers नहीं चुना गया है."

#: sphinx/builders/html/__init__.py:1241
#, python-format
msgid "Unknown math_renderer %r is given."
msgstr "अज्ञात math_renderer %r दिया गया."

#: sphinx/builders/html/__init__.py:1249
#, python-format
msgid "html_extra_path entry %r does not exist"
msgstr "html_extra_path प्रविष्टि %r का अस्तित्व नहीं है"

#: sphinx/builders/html/__init__.py:1253
#, python-format
msgid "html_extra_path entry %r is placed inside outdir"
msgstr "html_extra_path का प्रविष्टि %r outdir में है|  "

#: sphinx/builders/html/__init__.py:1262
#, python-format
msgid "html_static_path entry %r does not exist"
msgstr "html_static_path प्रविष्टि %r का अस्तित्व नहीं है"

#: sphinx/builders/html/__init__.py:1266
#, python-format
msgid "html_static_path entry %r is placed inside outdir"
msgstr "html_static_path का  प्रविष्टि %r outdir में है|  "

#: sphinx/builders/html/__init__.py:1275 sphinx/builders/latex/__init__.py:444
#, python-format
msgid "logo file %r does not exist"
msgstr "प्रतीकचिन्ह फाइल %r का अस्तित्व नहीं है"

#: sphinx/builders/html/__init__.py:1284
#, python-format
msgid "favicon file %r does not exist"
msgstr "इष्ट चिन्ह फाइल %r का अस्तित्व नहीं है"

#: sphinx/builders/html/__init__.py:1291
msgid ""
"HTML 4 is no longer supported by Sphinx. (\"html4_writer=True\" detected in "
"configuration options)"
msgstr ""

#: sphinx/builders/html/__init__.py:1306
#, python-format
msgid "%s %s documentation"
msgstr "%s %s दिग्दर्शिका"

#: sphinx/builders/latex/__init__.py:115
#, python-format
msgid "The LaTeX files are in %(outdir)s."
msgstr "लाटेक्स लेखपत्र %(outdir)s में हैं."

#: sphinx/builders/latex/__init__.py:117
msgid ""
"\n"
"Run 'make' in that directory to run these through (pdf)latex\n"
"(use `make latexpdf' here to do that automatically)."
msgstr "\nइन्हें (pdf)latex से चलाने के लिए उस निर्देशिका में 'मेक' आदेश चलायें\n(ऐसा स्वचालित रूप से करने के लिए यहाँ 'make latexpdf' आदेश का उपयोग करें)"

#: sphinx/builders/latex/__init__.py:152
msgid "no \"latex_documents\" config value found; no documents will be written"
msgstr "कोई \"latex_documents\" विन्यास मान नहीं मिला; कोई  नहीं लिखे जाएंगे"

#: sphinx/builders/latex/__init__.py:160
#, python-format
msgid "\"latex_documents\" config value references unknown document %s"
msgstr "\"latex_documents\" विन्यास मान अज्ञात लेखपत्र %s का सन्दर्भ है"

#: sphinx/builders/latex/__init__.py:196 sphinx/domains/std/__init__.py:559
#: sphinx/domains/std/__init__.py:571 sphinx/templates/latex/latex.tex_t:106
#: sphinx/themes/basic/genindex-single.html:30
#: sphinx/themes/basic/genindex-single.html:55
#: sphinx/themes/basic/genindex-split.html:11
#: sphinx/themes/basic/genindex-split.html:14
#: sphinx/themes/basic/genindex.html:11 sphinx/themes/basic/genindex.html:34
#: sphinx/themes/basic/genindex.html:67 sphinx/themes/basic/layout.html:138
#: sphinx/writers/texinfo.py:497
msgid "Index"
msgstr "अनुक्रमणिका"

#: sphinx/builders/latex/__init__.py:199 sphinx/templates/latex/latex.tex_t:91
msgid "Release"
msgstr "आवृत्ति"

#: sphinx/builders/latex/__init__.py:213 sphinx/writers/latex.py:370
#, python-format
msgid "no Babel option known for language %r"
msgstr "%r भाषा के लिए कोई बाबेल विकल्प नहीं "

#: sphinx/builders/latex/__init__.py:394
msgid "copying TeX support files"
msgstr "टेक्स सहायक फाइलों की प्रतिलिपि की जा रही है..."

#: sphinx/builders/latex/__init__.py:410
msgid "copying TeX support files..."
msgstr "टेक्स सहायक फाइलों की प्रतिलिपि की जा रही है..."

#: sphinx/builders/latex/__init__.py:423
msgid "copying additional files"
msgstr "अतिरिक्त फाइलों की प्रतिकृति बनाई जा रही है"

#: sphinx/builders/latex/__init__.py:466
#, python-format
msgid "Unknown configure key: latex_elements[%r], ignored."
msgstr ""

#: sphinx/builders/latex/__init__.py:474
#, python-format
msgid "Unknown theme option: latex_theme_options[%r], ignored."
msgstr ""

#: sphinx/builders/latex/theming.py:87
#, python-format
msgid "%r doesn't have \"theme\" setting"
msgstr "%r में कोई \"रूप\" मान नहीं है"

#: sphinx/builders/latex/theming.py:90
#, python-format
msgid "%r doesn't have \"%s\" setting"
msgstr "%r में कोई \"%s \" मान नहीं है"

#: sphinx/builders/latex/transforms.py:120
msgid "Failed to get a docname!"
msgstr ""

#: sphinx/builders/latex/transforms.py:121
msgid "Failed to get a docname for source {source!r}!"
msgstr ""

#: sphinx/builders/latex/transforms.py:482
#, python-format
msgid "No footnote was found for given reference node %r"
msgstr ""

#: sphinx/cmd/build.py:46
msgid "Exception occurred while building, starting debugger:"
msgstr "निर्माण के दौरान अपवाद घटित हुआ है, दोष-मुक्तक चालू किया जा रहा "

#: sphinx/cmd/build.py:61
msgid "Interrupted!"
msgstr "कार्य खंडित "

#: sphinx/cmd/build.py:63
msgid "reST markup error:"
msgstr "रेस्ट सुसज्जा त्रुटि:"

#: sphinx/cmd/build.py:69
msgid "Encoding error:"
msgstr "कूटलेखन त्रुटि:"

#: sphinx/cmd/build.py:72 sphinx/cmd/build.py:87
#, python-format
msgid ""
"The full traceback has been saved in %s, if you want to report the issue to "
"the developers."
msgstr "यदि आप इस विषय को कूटलिपिकारों के संज्ञान में लाना चाहते है तो   पिछला पूरा विवरण %s में सहेज दिया गया है"

#: sphinx/cmd/build.py:76
msgid "Recursion error:"
msgstr "पुनरावर्तन त्रुटि:"

#: sphinx/cmd/build.py:79
msgid ""
"This can happen with very large or deeply nested source files. You can "
"carefully increase the default Python recursion limit of 1000 in conf.py "
"with e.g.:"
msgstr ""

#: sphinx/cmd/build.py:84
msgid "Exception occurred:"
msgstr "अपवाद घटित:"

#: sphinx/cmd/build.py:90
msgid ""
"Please also report this if it was a user error, so that a better error "
"message can be provided next time."
msgstr "यदि यह प्रयोक्ता की गलती थी तो कृपया इसको भी रिपोर्ट करें ताकि अगली बार गलती होने पर अधिक अर्थपूर्ण सन्देश दिया जा सके."

#: sphinx/cmd/build.py:93
msgid ""
"A bug report can be filed in the tracker at <https://github.com/sphinx-"
"doc/sphinx/issues>. Thanks!"
msgstr "त्रुटि की सूचना <https://github.com/sphinx-doc/sphinx/issues> पर उपस्थित पंजिका में दर्ज की जा सकती है. धन्यवाद!"

#: sphinx/cmd/build.py:109
msgid "job number should be a positive number"
msgstr "कार्य संख्या एक धनात्मक संख्या होनी चाहिए"

#: sphinx/cmd/build.py:117 sphinx/cmd/quickstart.py:474
#: sphinx/ext/apidoc.py:317 sphinx/ext/autosummary/generate.py:689
msgid "For more information, visit <https://www.sphinx-doc.org/>."
msgstr ""

#: sphinx/cmd/build.py:118
msgid ""
"\n"
"Generate documentation from source files.\n"
"\n"
"sphinx-build generates documentation from the files in SOURCEDIR and places it\n"
"in OUTPUTDIR. It looks for 'conf.py' in SOURCEDIR for the configuration\n"
"settings. The 'sphinx-quickstart' tool may be used to generate template files,\n"
"including 'conf.py'\n"
"\n"
"sphinx-build can create documentation in different formats. A format is\n"
"selected by specifying the builder name on the command line; it defaults to\n"
"HTML. Builders can also perform other tasks related to documentation\n"
"processing.\n"
"\n"
"By default, everything that is outdated is built. Output only for selected\n"
"files can be built by specifying individual filenames.\n"
msgstr ""

#: sphinx/cmd/build.py:139
msgid "path to documentation source files"
msgstr "अभिलेख की स्रोत फाइलों का पथ"

#: sphinx/cmd/build.py:141
msgid "path to output directory"
msgstr "परिणाम निर्देशिका का पथ"

#: sphinx/cmd/build.py:143
msgid ""
"(optional) a list of specific files to rebuild. Ignored if --write-all is "
"specified"
msgstr ""

#: sphinx/cmd/build.py:146
msgid "general options"
msgstr "सामान्य विकल्प"

#: sphinx/cmd/build.py:149
msgid "builder to use (default: 'html')"
msgstr ""

#: sphinx/cmd/build.py:152
msgid ""
"run in parallel with N processes, when possible. 'auto' uses the number of "
"CPU cores"
msgstr ""

#: sphinx/cmd/build.py:155
msgid "write all files (default: only write new and changed files)"
msgstr "सभी फाइलें लिखें (मानक: केवल नई और परिवर्तित फाइलें लिखें)"

#: sphinx/cmd/build.py:158
msgid "don't use a saved environment, always read all files"
msgstr "सहेजी गयी परिस्थिति का प्रयोग न करें, सदैव सभी फाइलों को पढ़ें"

#: sphinx/cmd/build.py:161
msgid "path options"
msgstr ""

#: sphinx/cmd/build.py:163
msgid ""
"directory for doctree and environment files (default: OUTPUT_DIR/.doctrees)"
msgstr ""

#: sphinx/cmd/build.py:166
msgid "directory for the configuration file (conf.py) (default: SOURCE_DIR)"
msgstr ""

#: sphinx/cmd/build.py:171
msgid "use no configuration file, only use settings from -D options"
msgstr ""

#: sphinx/cmd/build.py:174
msgid "override a setting in configuration file"
msgstr "विन्यास फाइल के एक मान का उल्लंघन करें "

#: sphinx/cmd/build.py:177
msgid "pass a value into HTML templates"
msgstr "एच.टी.एम्.एल. के नमूने में राशि प्रेषित करें"

#: sphinx/cmd/build.py:180
msgid "define tag: include \"only\" blocks with TAG"
msgstr "नाम-पत्र परिभाषित करें: केवल नाम-पत्र वाले खण्डों का समावेश करें"

#: sphinx/cmd/build.py:182
msgid "nit-picky mode: warn about all missing references"
msgstr ""

#: sphinx/cmd/build.py:184
msgid "console output options"
msgstr "प्रदर्शित परिणामों के विकल्प"

#: sphinx/cmd/build.py:187
msgid "increase verbosity (can be repeated)"
msgstr "शब्द-प्रयोग बढ़ाएं (पुनरावृत्ति की जा सकती है) "

#: sphinx/cmd/build.py:189 sphinx/ext/apidoc.py:340
msgid "no output on stdout, just warnings on stderr"
msgstr "एस.टी.डी आउट #stdout# पर कोई परिणाम नहीं, एस.टी.डी एरर #stderr# पर चेतावनियाँ "

#: sphinx/cmd/build.py:191
msgid "no output at all, not even warnings"
msgstr "कुछ भी निर्गमित नहीं, यहाँ तक कि चेतावनी भी नहीं"

#: sphinx/cmd/build.py:194
msgid "do emit colored output (default: auto-detect)"
msgstr "रंगीन परिणाम ही दिखाएँ (मानक: स्वतः अनुमानित)"

#: sphinx/cmd/build.py:197
msgid "do not emit colored output (default: auto-detect)"
msgstr "रंगीन परिणाम नहीं दिखाएँ (मानक: स्वतः अनुमानित)"

#: sphinx/cmd/build.py:199
msgid "warning control options"
msgstr ""

#: sphinx/cmd/build.py:201
msgid "write warnings (and errors) to given file"
msgstr "चेतावनियाँ (और त्रुटियाँ) दी गई फाइल में लिखें"

#: sphinx/cmd/build.py:203
msgid "turn warnings into errors"
msgstr "चेतावनियों को अशुद्धि मानें"

#: sphinx/cmd/build.py:205
msgid "with --fail-on-warning, keep going when getting warnings"
msgstr ""

#: sphinx/cmd/build.py:207
msgid "show full traceback on exception"
msgstr "अपवाद होने पर पूरा विलोम-अनुगमन देखें"

#: sphinx/cmd/build.py:209
msgid "run Pdb on exception"
msgstr "अपवाद होने पर पी.डी.बी. चलाएं"

#: sphinx/cmd/build.py:244
msgid "cannot combine -a option and filenames"
msgstr "-a विकल्प और फाइल के नामों को सम्मिलित नहीं किया जा सकता"

#: sphinx/cmd/build.py:276
#, python-format
msgid "cannot open warning file %r: %s"
msgstr "चेतावनी फाइल %r नहीं खोली जा सकी: %s"

#: sphinx/cmd/build.py:296
msgid "-D option argument must be in the form name=value"
msgstr "-D विकल्प का मान नाम = मान के रूप में होना आवश्यक है"

#: sphinx/cmd/build.py:303
msgid "-A option argument must be in the form name=value"
msgstr "-A विकल्प का मान नाम = मान के रूप में होना आवश्यक है"

#: sphinx/cmd/quickstart.py:42
msgid "automatically insert docstrings from modules"
msgstr "प्रभागों में से डॉक्-स्ट्रिंग स्वतःसम्मिलित करें"

#: sphinx/cmd/quickstart.py:43
msgid "automatically test code snippets in doctest blocks"
msgstr "डॉक्-टेस्ट अंशों के निर्देश भाग की स्वतः जाँच करें"

#: sphinx/cmd/quickstart.py:44
msgid "link between Sphinx documentation of different projects"
msgstr "भिन्न परियोजनाओं के स्फिंक्स प्रलेखों का पारस्परिक सम्बन्ध करने दें"

#: sphinx/cmd/quickstart.py:45
msgid "write \"todo\" entries that can be shown or hidden on build"
msgstr "वह \"शेष\" प्रविष्टियाँ लिख लें, जिन्हें निर्माण के समय दिखाया या छिपाया जा सकता है"

#: sphinx/cmd/quickstart.py:46
msgid "checks for documentation coverage"
msgstr "प्रलेखों की व्याप्ति की जाँच करें"

#: sphinx/cmd/quickstart.py:47
msgid "include math, rendered as PNG or SVG images"
msgstr "गणित को सम्मिलित करें, पी.एन.जी. अथवा एस.वी.जी. में चित्रित"

#: sphinx/cmd/quickstart.py:48
msgid "include math, rendered in the browser by MathJax"
msgstr "गणित को सम्मिलित करें, दिग्दर्शक में मैथजाक्स #MathJax# द्वारा प्रदर्शित"

#: sphinx/cmd/quickstart.py:49
msgid "conditional inclusion of content based on config values"
msgstr "विन्यास मान के आधार पर सामिग्री का सशर्त समावेश"

#: sphinx/cmd/quickstart.py:50
msgid "include links to the source code of documented Python objects"
msgstr "पाइथन विषयवस्तुओं के प्रलेखों के स्रोत निर्देश की कड़ी जोड़ें"

#: sphinx/cmd/quickstart.py:51
msgid "create .nojekyll file to publish the document on GitHub pages"
msgstr "गिटहब GitHub पर लेखपत्र प्रकाशित करने के लिए .nojekyll फाइल बनाएं"

#: sphinx/cmd/quickstart.py:93
msgid "Please enter a valid path name."
msgstr "कृपया एक मान्य पथ का नाम दें"

#: sphinx/cmd/quickstart.py:109
msgid "Please enter some text."
msgstr "कृपया कुछ वाक्यांश लिखें"

#: sphinx/cmd/quickstart.py:116
#, python-format
msgid "Please enter one of %s."
msgstr "%s में से एक चुनें"

#: sphinx/cmd/quickstart.py:123
msgid "Please enter either 'y' or 'n'."
msgstr "कृपया हाँ के लिए 'y' अथवा नहीं के लिए 'n' मात्र दें. "

#: sphinx/cmd/quickstart.py:129
msgid "Please enter a file suffix, e.g. '.rst' or '.txt'."
msgstr "कृपया एक फाइल प्रत्यय दें, जैसे कि '.rst' अथवा '.txt'."

#: sphinx/cmd/quickstart.py:208
#, python-format
msgid "Welcome to the Sphinx %s quickstart utility."
msgstr "स्फिंक्स %s त्वरित-आरंभ #sphinx-quickstart# उपकरण के लिए अभिनन्दन"

#: sphinx/cmd/quickstart.py:210
msgid ""
"Please enter values for the following settings (just press Enter to\n"
"accept a default value, if one is given in brackets)."
msgstr "कृपया निम्न विन्यासों के लिए मान प्रदान करें (मानक मान, यदि कोष्ठक में हो तो, स्वीकार करने के लिए एन्टर दबाएँ)"

#: sphinx/cmd/quickstart.py:215
#, python-format
msgid "Selected root path: %s"
msgstr "चुना हुआ बुनियादी तथा मूल स्थान: %s"

#: sphinx/cmd/quickstart.py:218
msgid "Enter the root path for documentation."
msgstr "आलेख का बुनियादी स्थान बताएं."

#: sphinx/cmd/quickstart.py:219
msgid "Root path for the documentation"
msgstr "आलेख का बुनियादी पथ"

#: sphinx/cmd/quickstart.py:224
msgid "Error: an existing conf.py has been found in the selected root path."
msgstr "त्रुटि: एक मौजूदा conf.py फाइल दिए गए मूल पथ में प्राप्त हुई है."

#: sphinx/cmd/quickstart.py:226
msgid "sphinx-quickstart will not overwrite existing Sphinx projects."
msgstr "स्फिंक्स-त्वरित-आरम्भ #sphinx-quickstart# मौजूदा स्फिंक्स परियोजनाओं पर पुनर्लेखन नहीं करेगा."

#: sphinx/cmd/quickstart.py:228
msgid "Please enter a new root path (or just Enter to exit)"
msgstr "कृपया एक नया मूल पथ दें (अथवा निकलने हेतु सिर्फ एन्टर #Enter# कर दें)"

#: sphinx/cmd/quickstart.py:235
msgid ""
"You have two options for placing the build directory for Sphinx output.\n"
"Either, you use a directory \"_build\" within the root path, or you separate\n"
"\"source\" and \"build\" directories within the root path."
msgstr "आपके पास Sphinx द्वारा बनाई गई फाइलों को सहेजने के लिए दो विकल्प हैं.\nया तो आप मूल स्थान में ही \"_build\" निर्देशिका प्रयोग करें, अथवा\nमूल पथ में भिन्न \"स्रोत\" और \"build\" निर्देशिका प्रयोग करें."

#: sphinx/cmd/quickstart.py:238
msgid "Separate source and build directories (y/n)"
msgstr "विभिन्न स्रोत और निर्माण डायरेक्टरी (y/n)"

#: sphinx/cmd/quickstart.py:242
msgid ""
"Inside the root directory, two more directories will be created; \"_templates\"\n"
"for custom HTML templates and \"_static\" for custom stylesheets and other static\n"
"files. You can enter another prefix (such as \".\") to replace the underscore."
msgstr "मूल निर्देशिका के अन्दर, दो और निर्देशिका बनाई जाएँगी;\nपरिवर्धित एच.टी.एम्.एल. नमूनों के लिए \"_templates\" और परिवर्धित रुपपत्रों और अन्य स्थैतिक फाइलों के लिए \"_static\"\nआप अधोरेखा के स्थान पर अन्य पूर्व-प्रत्यय (जैसे कि \".\") का प्रयोग कर सकते हैं."

#: sphinx/cmd/quickstart.py:245
msgid "Name prefix for templates and static dir"
msgstr "नमूने और स्थैतिक डायरेक्टरी के लिए पूर्व-प्रत्यय"

#: sphinx/cmd/quickstart.py:249
msgid ""
"The project name will occur in several places in the built documentation."
msgstr "परियोजना का नाम बनाये गए प्रपत्रों में बहुत से स्थानों पर प्रयुक्त होगा."

#: sphinx/cmd/quickstart.py:250
msgid "Project name"
msgstr "परियोजना का नाम"

#: sphinx/cmd/quickstart.py:252
msgid "Author name(s)"
msgstr "लेखक(कों) का नाम"

#: sphinx/cmd/quickstart.py:256
msgid ""
"Sphinx has the notion of a \"version\" and a \"release\" for the\n"
"software. Each version can have multiple releases. For example, for\n"
"Python the version is something like 2.5 or 3.0, while the release is\n"
"something like 2.5.1 or 3.0a1. If you don't need this dual structure,\n"
"just set both to the same value."
msgstr ""

#: sphinx/cmd/quickstart.py:261
msgid "Project version"
msgstr "परियोजना संस्करण"

#: sphinx/cmd/quickstart.py:263
msgid "Project release"
msgstr "परियोजना आवृत्ति"

#: sphinx/cmd/quickstart.py:267
msgid ""
"If the documents are to be written in a language other than English,\n"
"you can select a language here by its language code. Sphinx will then\n"
"translate text that it generates into that language.\n"
"\n"
"For a list of supported codes, see\n"
"https://www.sphinx-doc.org/en/master/usage/configuration.html#confval-language."
msgstr "यदि प्रलेखों को अंग्रेजी के अलावा अन्य किसी भाषा में लिखा जाना है,\nतो यहाँ पर आप भाषा का कूटशब्द दे सकते हैं. स्फिंक्स तदपुरांत,\nजो वाक्यांश बनाता है उसे उस भाषा में अनुवादित करेगा.\n\nमान्य भाषा कूटशब्द सूची यहाँ पर देखें\nhttps://www.sphinx-doc.org/en/master/usage/configuration.html#confval-language."

#: sphinx/cmd/quickstart.py:275
msgid "Project language"
msgstr "परियोजना की भाषा"

#: sphinx/cmd/quickstart.py:281
msgid ""
"The file name suffix for source files. Commonly, this is either \".txt\"\n"
"or \".rst\". Only files with this suffix are considered documents."
msgstr ""

#: sphinx/cmd/quickstart.py:283
msgid "Source file suffix"
msgstr "स्रोत फाइल का प्रत्यय"

#: sphinx/cmd/quickstart.py:287
msgid ""
"One document is special in that it is considered the top node of the\n"
"\"contents tree\", that is, it is the root of the hierarchical structure\n"
"of the documents. Normally, this is \"index\", but if your \"index\"\n"
"document is a custom template, you can also set this to another filename."
msgstr ""

#: sphinx/cmd/quickstart.py:291
msgid "Name of your master document (without suffix)"
msgstr "आपने मुख्य लेखपत्र का नाम दें (प्रत्यय रहित)"

#: sphinx/cmd/quickstart.py:296
#, python-format
msgid ""
"Error: the master file %s has already been found in the selected root path."
msgstr "त्रुटि: मुख्य फाइल %s चुने हुए मूल पथ में पहले से उपलब्ध है."

#: sphinx/cmd/quickstart.py:298
msgid "sphinx-quickstart will not overwrite the existing file."
msgstr "स्फिंक्स-त्वरित-आरम्भ मौजूदा फाइलों पर पुनर्लेखन नहीं करेगा."

#: sphinx/cmd/quickstart.py:300
msgid ""
"Please enter a new file name, or rename the existing file and press Enter"
msgstr "कृपया एक नया फाइल नाम दें, अथवा मौजूदा फाइल का पुनर्नामकरण करें और एन्टर दबाएँ"

#: sphinx/cmd/quickstart.py:304
msgid "Indicate which of the following Sphinx extensions should be enabled:"
msgstr "इनमें से कौन सा स्फिंक्स आयाम प्रयोग करना है, इंगित करें:"

#: sphinx/cmd/quickstart.py:312
msgid ""
"Note: imgmath and mathjax cannot be enabled at the same time. imgmath has "
"been deselected."
msgstr "टिप्पणी: imgmath और mathjax एक साथ समर्थ नहीं हो सकते. imgmath को अचिन्हित कर दिया गया है."

#: sphinx/cmd/quickstart.py:318
msgid ""
"A Makefile and a Windows command file can be generated for you so that you\n"
"only have to run e.g. `make html' instead of invoking sphinx-build\n"
"directly."
msgstr ""

#: sphinx/cmd/quickstart.py:321
msgid "Create Makefile? (y/n)"
msgstr "मेकफाइल बनाएं? (हाँ के लिए y/ ना के लिए n)"

#: sphinx/cmd/quickstart.py:324
msgid "Create Windows command file? (y/n)"
msgstr "विंडोज़ कमांड फाइल बनाएं? (हाँ के लिए y/ ना के लिए n)"

#: sphinx/cmd/quickstart.py:368 sphinx/ext/apidoc.py:93
#, python-format
msgid "Creating file %s."
msgstr "फाइल बनाई जा रही है ...%s"

#: sphinx/cmd/quickstart.py:373 sphinx/ext/apidoc.py:90
#, python-format
msgid "File %s already exists, skipping."
msgstr "फाइल %s पहले से उपस्थित है, छोड़ दी गई."

#: sphinx/cmd/quickstart.py:418
msgid "Finished: An initial directory structure has been created."
msgstr "समाप्त: एक प्रारंभिक निर्देशिका का ढांचा बना दिया गया है."

#: sphinx/cmd/quickstart.py:420
#, python-format
msgid ""
"You should now populate your master file %s and create other documentation\n"
"source files. "
msgstr ""

#: sphinx/cmd/quickstart.py:423
msgid ""
"Use the Makefile to build the docs, like so:\n"
"   make builder"
msgstr ""

#: sphinx/cmd/quickstart.py:426
#, python-format
msgid ""
"Use the sphinx-build command to build the docs, like so:\n"
"   sphinx-build -b builder %s %s"
msgstr ""

#: sphinx/cmd/quickstart.py:428
msgid ""
"where \"builder\" is one of the supported builders, e.g. html, latex or "
"linkcheck."
msgstr ""

#: sphinx/cmd/quickstart.py:464
msgid ""
"\n"
"Generate required files for a Sphinx project.\n"
"\n"
"sphinx-quickstart is an interactive tool that asks some questions about your\n"
"project and then generates a complete documentation directory and sample\n"
"Makefile to be used with sphinx-build.\n"
msgstr "\nस्फिंक्स परियोजना के लिए आवश्यक फाइल बनाएं.\n\nस्फिंक्स-त्वरित-आरम्भ एक संवादपूर्ण उपकरण है जो आपकी परियोजना के \nबारे में कुछ प्रश्न पूछकर पूरी प्रलेखों की निर्देशिका और नमूना मेकफाइल \nबना देता है जिसे स्फिंक्स-बिल्ड में प्रयोग किया जा सकता है.\n"

#: sphinx/cmd/quickstart.py:479
msgid "quiet mode"
msgstr "शांत ढंग "

#: sphinx/cmd/quickstart.py:484
msgid "project root"
msgstr ""

#: sphinx/cmd/quickstart.py:486
msgid "Structure options"
msgstr "ढांचे के विकल्प"

#: sphinx/cmd/quickstart.py:488
msgid "if specified, separate source and build dirs"
msgstr "यदि निर्दिष्ट हो तो विभिन्न स्रोत और निर्माण पथ"

#: sphinx/cmd/quickstart.py:490
msgid "if specified, create build dir under source dir"
msgstr ""

#: sphinx/cmd/quickstart.py:492
msgid "replacement for dot in _templates etc."
msgstr "_templates आदि में बिंदु का बदलाव"

#: sphinx/cmd/quickstart.py:494
msgid "Project basic options"
msgstr "परोयोजना के मूलभूत विकल्प"

#: sphinx/cmd/quickstart.py:496
msgid "project name"
msgstr "परियोजना का नाम"

#: sphinx/cmd/quickstart.py:498
msgid "author names"
msgstr "लेखकों के नाम"

#: sphinx/cmd/quickstart.py:500
msgid "version of project"
msgstr "परियोजना का संस्करण"

#: sphinx/cmd/quickstart.py:502
msgid "release of project"
msgstr "परियोजना की आवृत्ति"

#: sphinx/cmd/quickstart.py:504
msgid "document language"
msgstr "लेखपत्र की भाषा"

#: sphinx/cmd/quickstart.py:506
msgid "source file suffix"
msgstr "स्रोत फाइल का प्रत्यय"

#: sphinx/cmd/quickstart.py:508
msgid "master document name"
msgstr "मुख्य लेखपत्र का नाम"

#: sphinx/cmd/quickstart.py:510
msgid "use epub"
msgstr "ई-पब प्रयोग करें"

#: sphinx/cmd/quickstart.py:512
msgid "Extension options"
msgstr "आयाम के विकल्प"

#: sphinx/cmd/quickstart.py:516 sphinx/ext/apidoc.py:400
#, python-format
msgid "enable %s extension"
msgstr "आयाम %s सक्षम करें"

#: sphinx/cmd/quickstart.py:518 sphinx/ext/apidoc.py:396
msgid "enable arbitrary extensions"
msgstr "स्वेच्छित आयाम सक्षम करें"

#: sphinx/cmd/quickstart.py:520
msgid "Makefile and Batchfile creation"
msgstr "मेकफाइल और बैचफाइल का सर्जन"

#: sphinx/cmd/quickstart.py:522
msgid "create makefile"
msgstr "मेकफाइल बनाएं"

#: sphinx/cmd/quickstart.py:524
msgid "do not create makefile"
msgstr "मेकफाइल नहीं बनाएं"

#: sphinx/cmd/quickstart.py:526
msgid "create batchfile"
msgstr "बैचफाइल बनाएं"

#: sphinx/cmd/quickstart.py:529
msgid "do not create batchfile"
msgstr "बैचफाइल नहीं बनाएं"

#: sphinx/cmd/quickstart.py:532
msgid "use make-mode for Makefile/make.bat"
msgstr "Makefile/make.bat के लिए make-mode का प्रयोग करें"

#: sphinx/cmd/quickstart.py:535
msgid "do not use make-mode for Makefile/make.bat"
msgstr "Makefile/make.bat के लिए make-mode का प्रयोग नहीं करें"

#: sphinx/cmd/quickstart.py:537 sphinx/ext/apidoc.py:402
msgid "Project templating"
msgstr "परियोजना नमूनावृत्ति"

#: sphinx/cmd/quickstart.py:540 sphinx/ext/apidoc.py:405
msgid "template directory for template files"
msgstr "नमूना फाइलों के लिए नमूना निर्देशिका"

#: sphinx/cmd/quickstart.py:543
msgid "define a template variable"
msgstr "नमूना चर-पद का निरूपण करें"

#: sphinx/cmd/quickstart.py:576
msgid "\"quiet\" is specified, but any of \"project\" or \"author\" is not specified."
msgstr "\"शांत\" निर्दिष्ट है, परन्तु कोई भी \"परियोजना\" अथवा \"लेखक\" निर्दिष्ट नहीं है."

#: sphinx/cmd/quickstart.py:590
msgid ""
"Error: specified path is not a directory, or sphinx files already exist."
msgstr "त्रुटि: दिया गया पथ निर्देशिका नहीं है, अथवा स्फिंक्स फाइलें पहले से उपस्थित हैं."

#: sphinx/cmd/quickstart.py:592
msgid ""
"sphinx-quickstart only generate into a empty directory. Please specify a new"
" root path."
msgstr "स्फिंक्स-त्वरित-आरम्भ केवल एक खाली निर्देशिका में कार्यशील हो सकती है. कृपया एक नया मूल पथ निर्दिष्ट करें."

#: sphinx/cmd/quickstart.py:607
#, python-format
msgid "Invalid template variable: %s"
msgstr "अमान्य नमूना चर-पद: %s"

#: sphinx/directives/code.py:61
msgid "non-whitespace stripped by dedent"
msgstr ""

#: sphinx/directives/code.py:82
#, python-format
msgid "Invalid caption: %s"
msgstr "अमान्य शीर्षक: %s"

#: sphinx/directives/code.py:127 sphinx/directives/code.py:277
#: sphinx/directives/code.py:453
#, python-format
msgid "line number spec is out of range(1-%d): %r"
msgstr "पंक्ति संख्या का ब्यौरा सीमा से बाहर है (1-%d): %r"

#: sphinx/directives/code.py:206
#, python-format
msgid "Cannot use both \"%s\" and \"%s\" options"
msgstr "दोनों \"%s\" और \"%s\" विकल्पों का प्रयोग नहीं किया जा सकता"

#: sphinx/directives/code.py:220
#, python-format
msgid "Include file %r not found or reading it failed"
msgstr "समावेशित फाइल %r नहीं मिली अथवा पढने में असफलता मिली"

#: sphinx/directives/code.py:223
#, python-format
msgid ""
"Encoding %r used for reading included file %r seems to be wrong, try giving "
"an :encoding: option"
msgstr "कूटलेखन %r जो कि सम्मिलित फाइल %r में प्रयुक्त है, अशुद्ध प्रतीत हो रही है, एक :encoding: विकल्प देकर प्रयत्न करें"

#: sphinx/directives/code.py:260
#, python-format
msgid "Object named %r not found in include file %r"
msgstr "%r नामक विषयवस्तु सम्मिलित फाइल %r में नहीं मिली"

#: sphinx/directives/code.py:286
msgid "Cannot use \"lineno-match\" with a disjoint set of \"lines\""
msgstr "\"lineno-match\" का प्रयोग बिना जुडी \"lines\" के युग्म के साथ नहीं हो सकता"

#: sphinx/directives/code.py:291
#, python-format
msgid "Line spec %r: no lines pulled from include file %r"
msgstr "लाइन ब्यौरा %r: सम्मिलित फाइल %r से कोई लाइन नहीं ली जा सकीं"

#: sphinx/directives/other.py:120
#, python-format
msgid "toctree glob pattern %r didn't match any documents"
msgstr ""

#: sphinx/directives/other.py:146 sphinx/environment/adapters/toctree.py:324
#, python-format
msgid "toctree contains reference to excluded document %r"
msgstr "विषय-सूची-संरचना में छोड़े गए लेखपत्र %r का सन्दर्भ है"

#: sphinx/directives/other.py:149 sphinx/environment/adapters/toctree.py:328
#, python-format
msgid "toctree contains reference to nonexisting document %r"
msgstr "विषय-सूची-संरचना में अविद्यमान लेखपत्र %r का सन्दर्भ है"

#: sphinx/directives/other.py:160
#, python-format
msgid "duplicated entry found in toctree: %s"
msgstr ""

#: sphinx/directives/other.py:193
msgid "Section author: "
msgstr "भाग के लेखक:"

#: sphinx/directives/other.py:195
msgid "Module author: "
msgstr "प्रभाग लेखक:"

#: sphinx/directives/other.py:197
msgid "Code author: "
msgstr "निर्देश लेखक:"

#: sphinx/directives/other.py:199
msgid "Author: "
msgstr "लेखक:"

#: sphinx/directives/other.py:275
msgid ".. acks content is not a list"
msgstr ""

#: sphinx/directives/other.py:301
msgid ".. hlist content is not a list"
msgstr ""

#: sphinx/directives/patches.py:66
msgid ""
"\":file:\" option for csv-table directive now recognizes an absolute path as"
" a relative path from source directory. Please update your document."
msgstr ""

#: sphinx/domains/__init__.py:397
#, python-format
msgid "%s %s"
msgstr "%s %s"

#: sphinx/domains/changeset.py:23
#, python-format
msgid "Added in version %s"
msgstr ""

#: sphinx/domains/changeset.py:24
#, python-format
msgid "Changed in version %s"
msgstr "संस्करण %s से अलग "

#: sphinx/domains/changeset.py:25
#, python-format
msgid "Deprecated since version %s"
msgstr "संस्करण %s से प्रतिबंधित "

#: sphinx/domains/changeset.py:26
#, python-format
msgid "Removed in version %s"
msgstr ""

#: sphinx/domains/citation.py:71
#, python-format
msgid "duplicate citation %s, other instance in %s"
msgstr "प्रतिरूप उद्धरण %s, दूसरी प्रतिकृति %s में है "

#: sphinx/domains/citation.py:82
#, python-format
msgid "Citation [%s] is not referenced."
msgstr "उद्धरण [%s] सन्दर्भ कहीं नहीं है"

#: sphinx/domains/javascript.py:165
#, python-format
msgid "%s() (built-in function)"
msgstr "%s() (अंतर्निर्मित फंक्शन)"

#: sphinx/domains/javascript.py:166 sphinx/domains/python/__init__.py:240
#, python-format
msgid "%s() (%s method)"
msgstr "%s() (%s विधि)"

#: sphinx/domains/javascript.py:168
#, python-format
msgid "%s() (class)"
msgstr "%s() (वर्ग)"

#: sphinx/domains/javascript.py:170
#, python-format
msgid "%s (global variable or constant)"
msgstr "%s (वैश्विक चरपद अथवा अचर) "

#: sphinx/domains/javascript.py:172 sphinx/domains/python/__init__.py:325
#, python-format
msgid "%s (%s attribute)"
msgstr "%s (%s लक्षण)"

#: sphinx/domains/javascript.py:255
msgid "Arguments"
msgstr "चर "

#: sphinx/domains/cpp/__init__.py:350 sphinx/domains/javascript.py:258
msgid "Throws"
msgstr "देता है "

#: sphinx/domains/c/__init__.py:251 sphinx/domains/cpp/__init__.py:361
#: sphinx/domains/javascript.py:261 sphinx/domains/python/_object.py:175
msgid "Returns"
msgstr "प्रदत्त "

#: sphinx/domains/c/__init__.py:253 sphinx/domains/javascript.py:263
#: sphinx/domains/python/_object.py:177
msgid "Return type"
msgstr "प्रदत्त प्रकार "

#: sphinx/domains/javascript.py:331
#, python-format
msgid "%s (module)"
msgstr "%s (प्रभाग)"

#: sphinx/domains/c/__init__.py:622 sphinx/domains/cpp/__init__.py:764
#: sphinx/domains/javascript.py:368 sphinx/domains/python/__init__.py:574
msgid "function"
msgstr "फंक्शन"

#: sphinx/domains/javascript.py:369 sphinx/domains/python/__init__.py:578
msgid "method"
msgstr "पद्धति"

#: sphinx/domains/cpp/__init__.py:762 sphinx/domains/javascript.py:370
#: sphinx/domains/python/__init__.py:576
msgid "class"
msgstr "वर्ग"

#: sphinx/domains/javascript.py:371 sphinx/domains/python/__init__.py:575
msgid "data"
msgstr "आंकड़े "

#: sphinx/domains/javascript.py:372 sphinx/domains/python/__init__.py:581
msgid "attribute"
msgstr "लक्षण"

#: sphinx/domains/javascript.py:373 sphinx/domains/python/__init__.py:583
msgid "module"
msgstr "प्रभाग"

#: sphinx/domains/javascript.py:404
#, python-format
msgid "duplicate %s description of %s, other %s in %s"
msgstr ""

#: sphinx/domains/math.py:63
#, python-format
msgid "duplicate label of equation %s, other instance in %s"
msgstr "समीकरण का प्रतिरूप शीर्षक %s, दूसरी प्रतिकृति %s में है "

#: sphinx/domains/math.py:118 sphinx/writers/latex.py:2252
#, python-format
msgid "Invalid math_eqref_format: %r"
msgstr "अमान्य math_eqref_format: %r"

#: sphinx/domains/rst.py:127 sphinx/domains/rst.py:184
#, python-format
msgid "%s (directive)"
msgstr "%s (निर्देश)"

#: sphinx/domains/rst.py:185 sphinx/domains/rst.py:189
#, python-format
msgid ":%s: (directive option)"
msgstr ""

#: sphinx/domains/rst.py:213
#, python-format
msgid "%s (role)"
msgstr "%s (भूमिका)"

#: sphinx/domains/rst.py:223
msgid "directive"
msgstr "निर्देश"

#: sphinx/domains/rst.py:224
msgid "directive-option"
msgstr ""

#: sphinx/domains/rst.py:225
msgid "role"
msgstr "भूमिका"

#: sphinx/domains/rst.py:247
#, python-format
msgid "duplicate description of %s %s, other instance in %s"
msgstr ""

#: sphinx/domains/c/__init__.py:146
#, python-format
msgid "%s (C %s)"
msgstr ""

#: sphinx/domains/c/__init__.py:207 sphinx/domains/c/_symbol.py:552
#, python-format
msgid ""
"Duplicate C declaration, also defined at %s:%s.\n"
"Declaration is '.. c:%s:: %s'."
msgstr ""

#: sphinx/domains/c/__init__.py:245 sphinx/domains/cpp/__init__.py:344
#: sphinx/domains/python/_object.py:163 sphinx/ext/napoleon/docstring.py:762
msgid "Parameters"
msgstr "मापदण्ड"

#: sphinx/domains/c/__init__.py:248 sphinx/domains/cpp/__init__.py:357
msgid "Return values"
msgstr ""

#: sphinx/domains/c/__init__.py:620 sphinx/domains/cpp/__init__.py:765
msgid "member"
msgstr "सदस्य"

#: sphinx/domains/c/__init__.py:621
msgid "variable"
msgstr "चर पद"

#: sphinx/domains/c/__init__.py:623
msgid "macro"
msgstr "मैक्रो"

#: sphinx/domains/c/__init__.py:624
msgid "struct"
msgstr ""

#: sphinx/domains/c/__init__.py:625 sphinx/domains/cpp/__init__.py:763
msgid "union"
msgstr "युग्म"

#: sphinx/domains/c/__init__.py:626 sphinx/domains/cpp/__init__.py:768
msgid "enum"
msgstr "गणक"

#: sphinx/domains/c/__init__.py:627 sphinx/domains/cpp/__init__.py:769
msgid "enumerator"
msgstr "प्रगणक "

#: sphinx/domains/c/__init__.py:628 sphinx/domains/cpp/__init__.py:766
msgid "type"
msgstr "प्रकार"

#: sphinx/domains/c/__init__.py:630 sphinx/domains/cpp/__init__.py:771
msgid "function parameter"
msgstr ""

#: sphinx/domains/cpp/__init__.py:63
msgid "Template Parameters"
msgstr "नमूना मानदण्ड "

#: sphinx/domains/cpp/__init__.py:185
#, python-format
msgid "%s (C++ %s)"
msgstr "%s (C++ %s)"

#: sphinx/domains/cpp/__init__.py:268 sphinx/domains/cpp/_symbol.py:790
#, python-format
msgid ""
"Duplicate C++ declaration, also defined at %s:%s.\n"
"Declaration is '.. cpp:%s:: %s'."
msgstr ""

#: sphinx/domains/cpp/__init__.py:767
msgid "concept"
msgstr "अवधारणा "

#: sphinx/domains/cpp/__init__.py:772
msgid "template parameter"
msgstr ""

#: sphinx/domains/python/__init__.py:94 sphinx/domains/python/__init__.py:231
#, python-format
msgid "%s() (in module %s)"
msgstr "%s() (%s प्रभाग में )"

#: sphinx/domains/python/__init__.py:154 sphinx/domains/python/__init__.py:321
#: sphinx/domains/python/__init__.py:372
#, python-format
msgid "%s (in module %s)"
msgstr "%s (%s प्रभाग में )"

#: sphinx/domains/python/__init__.py:156
#, python-format
msgid "%s (built-in variable)"
msgstr "%s (अंतर्निर्मित चर पद)"

#: sphinx/domains/python/__init__.py:181
#, python-format
msgid "%s (built-in class)"
msgstr "%s (अंतर्निर्मित वर्ग)"

#: sphinx/domains/python/__init__.py:182
#, python-format
msgid "%s (class in %s)"
msgstr "%s (%s वर्ग में)"

#: sphinx/domains/python/__init__.py:236
#, python-format
msgid "%s() (%s class method)"
msgstr "%s() (%s वर्ग विधि) "

#: sphinx/domains/python/__init__.py:238
#, python-format
msgid "%s() (%s static method)"
msgstr "%s() (%s स्थैतिक विधि)"

#: sphinx/domains/python/__init__.py:376
#, python-format
msgid "%s (%s property)"
msgstr ""

#: sphinx/domains/python/__init__.py:502
msgid "Python Module Index"
msgstr "पाइथन प्रभाग सूची"

#: sphinx/domains/python/__init__.py:503
msgid "modules"
msgstr "प्रभाग"

#: sphinx/domains/python/__init__.py:552
msgid "Deprecated"
msgstr "अवमानित "

#: sphinx/domains/python/__init__.py:577
msgid "exception"
msgstr "अपवाद "

#: sphinx/domains/python/__init__.py:579
msgid "class method"
msgstr "वर्ग विधि"

#: sphinx/domains/python/__init__.py:580
msgid "static method"
msgstr "स्थैतिक पद्धति"

#: sphinx/domains/python/__init__.py:582
msgid "property"
msgstr ""

#: sphinx/domains/python/__init__.py:640
#, python-format
msgid ""
"duplicate object description of %s, other instance in %s, use :no-index: for"
" one of them"
msgstr ""

#: sphinx/domains/python/__init__.py:760
#, python-format
msgid "more than one target found for cross-reference %r: %s"
msgstr "पारस्परिक-सन्दर्भों के लिए एक से अधिक लक्ष्य मिले %r: %s"

#: sphinx/domains/python/__init__.py:821
msgid " (deprecated)"
msgstr "(अवमानित)"

#: sphinx/domains/python/_object.py:168
msgid "Variables"
msgstr "चर पद "

#: sphinx/domains/python/_object.py:172
msgid "Raises"
msgstr "उभारता है "

#: sphinx/domains/std/__init__.py:80 sphinx/domains/std/__init__.py:97
#, python-format
msgid "environment variable; %s"
msgstr "परिस्थिति चर पद; %s"

#: sphinx/domains/std/__init__.py:157
#, python-format
msgid ""
"Malformed option description %r, should look like \"opt\", \"-opt args\", \""
"--opt args\", \"/opt args\" or \"+opt args\""
msgstr "अशुद्ध रूप विकल्प विवरण %r, अपेक्षित प्रारूप \"opt\", \"-opt args\", \"--opt args\", \"/opt args\" अथवा \"+opt args\""

#: sphinx/domains/std/__init__.py:228
#, python-format
msgid "%s command line option"
msgstr ""

#: sphinx/domains/std/__init__.py:230
msgid "command line option"
msgstr ""

#: sphinx/domains/std/__init__.py:348
msgid "glossary term must be preceded by empty line"
msgstr ""

#: sphinx/domains/std/__init__.py:356
msgid "glossary terms must not be separated by empty lines"
msgstr ""

#: sphinx/domains/std/__init__.py:362 sphinx/domains/std/__init__.py:375
msgid "glossary seems to be misformatted, check indentation"
msgstr ""

#: sphinx/domains/std/__init__.py:518
msgid "glossary term"
msgstr "पारिभाषिक पद"

#: sphinx/domains/std/__init__.py:519
msgid "grammar token"
msgstr "व्याकरण संकेत "

#: sphinx/domains/std/__init__.py:520
msgid "reference label"
msgstr "सन्दर्भ शीर्षक"

#: sphinx/domains/std/__init__.py:522
msgid "environment variable"
msgstr "परिस्थिति चर पद "

#: sphinx/domains/std/__init__.py:523
msgid "program option"
msgstr "प्रोग्राम विकल्प "

#: sphinx/domains/std/__init__.py:524
msgid "document"
msgstr "लेखपत्र"

#: sphinx/domains/std/__init__.py:560 sphinx/domains/std/__init__.py:572
msgid "Module Index"
msgstr "प्रभाग सूची"

#: sphinx/domains/std/__init__.py:561 sphinx/domains/std/__init__.py:573
#: sphinx/themes/basic/defindex.html:25
msgid "Search Page"
msgstr "खोज पृष्ठ"

#: sphinx/domains/std/__init__.py:616 sphinx/domains/std/__init__.py:722
#: sphinx/ext/autosectionlabel.py:53
#, python-format
msgid "duplicate label %s, other instance in %s"
msgstr "प्रतिरूप शीर्षक %s, दूसरी प्रतिकृति %s में है "

#: sphinx/domains/std/__init__.py:635
#, python-format
msgid "duplicate %s description of %s, other instance in %s"
msgstr ""

#: sphinx/domains/std/__init__.py:841
msgid "numfig is disabled. :numref: is ignored."
msgstr "numfig असमर्थ है. :numref: उपेक्षित है."

#: sphinx/domains/std/__init__.py:849
#, python-format
msgid "Failed to create a cross reference. Any number is not assigned: %s"
msgstr ""

#: sphinx/domains/std/__init__.py:861
#, python-format
msgid "the link has no caption: %s"
msgstr "कड़ी का कोई शीर्षक नहीं है: %s"

#: sphinx/domains/std/__init__.py:875
#, python-format
msgid "invalid numfig_format: %s (%r)"
msgstr "अमान्य numfig_format: %s (%r)"

#: sphinx/domains/std/__init__.py:878
#, python-format
msgid "invalid numfig_format: %s"
msgstr "अमान्य numfig_format: %s"

#: sphinx/domains/std/__init__.py:1109
#, python-format
msgid "undefined label: %r"
msgstr ""

#: sphinx/domains/std/__init__.py:1111
#, python-format
msgid "Failed to create a cross reference. A title or caption not found: %r"
msgstr ""

#: sphinx/environment/__init__.py:71
msgid "new config"
msgstr "नव विन्यास"

#: sphinx/environment/__init__.py:72
msgid "config changed"
msgstr "विन्यास परिवर्तित"

#: sphinx/environment/__init__.py:73
msgid "extensions changed"
msgstr "आयाम परिवर्तित"

#: sphinx/environment/__init__.py:279
msgid "build environment version not current"
msgstr "निर्मित परिस्थिति वर्तमान संस्करण नहीं है "

#: sphinx/environment/__init__.py:281
msgid "source directory has changed"
msgstr "स्रोत निर्देशिका परिवर्तित हो चुकी है "

#: sphinx/environment/__init__.py:360
msgid ""
"This environment is incompatible with the selected builder, please choose "
"another doctree directory."
msgstr "यह परिस्थिति चुने गए निर्माता से मेल नहीं खाती, कृपया दूसरी डॉक-ट्री निर्देशिका चुनें. "

#: sphinx/environment/__init__.py:459
#, python-format
msgid "Failed to scan documents in %s: %r"
msgstr "लेखपत्रों के पर्यवेक्षण में असफलता %s: %r"

#: sphinx/environment/__init__.py:596
#, python-format
msgid "Domain %r is not registered"
msgstr "अधिकारक्षेत्र %r पंजीकृत नहीं है"

#: sphinx/environment/__init__.py:730
msgid "document isn't included in any toctree"
msgstr "लेखपत्र किसी भी विषय-सूची-संरचना में सम्मिलित नहीं है"

#: sphinx/environment/__init__.py:766
msgid "self referenced toctree found. Ignored."
msgstr "स्वयं-संदर्भित विषय-सूची-संरचना मिली है. उपेक्षा की गई."

#: sphinx/environment/adapters/indexentries.py:69
#, python-format
msgid "see %s"
msgstr "%s देखिए"

#: sphinx/environment/adapters/indexentries.py:73
#, python-format
msgid "see also %s"
msgstr "%s भी देखिए"

#: sphinx/environment/adapters/indexentries.py:76
#, python-format
msgid "unknown index entry type %r"
msgstr "अनुक्रमणिका की प्रविष्टि का प्रकार अज्ञात %r"

#: sphinx/environment/adapters/indexentries.py:187
#: sphinx/templates/latex/sphinxmessages.sty_t:11
msgid "Symbols"
msgstr "संकेत "

#: sphinx/environment/adapters/toctree.py:297
#, python-format
msgid "circular toctree references detected, ignoring: %s <- %s"
msgstr "पारस्परिक संदर्भित विषय-सूची-संरचना सन्दर्भ पाए गए, उपेक्षा की जा रही है: %s <- %s"

#: sphinx/environment/adapters/toctree.py:317
#, python-format
msgid ""
"toctree contains reference to document %r that doesn't have a title: no link"
" will be generated"
msgstr "विषय-सूची-संरचना में लेखपत्र %r, जिसका कोई शीर्षक नहीं है, का सन्दर्भ है: कोई सम्बन्ध नहीं बनाया जा सकेगा"

#: sphinx/environment/adapters/toctree.py:326
#, python-format
msgid "toctree contains reference to non-included document %r"
msgstr ""

#: sphinx/environment/collectors/asset.py:89
#, python-format
msgid "image file not readable: %s"
msgstr "चित्र फाइल पठनीय नहीं है: %s"

#: sphinx/environment/collectors/asset.py:108
#, python-format
msgid "image file %s not readable: %s"
msgstr "चित्र फाइल %s पठनीय नहीं है: %s"

#: sphinx/environment/collectors/asset.py:134
#, python-format
msgid "download file not readable: %s"
msgstr "उतारी गई फाइल पठनीय नहीं है: %s"

#: sphinx/environment/collectors/toctree.py:225
#, python-format
msgid "%s is already assigned section numbers (nested numbered toctree?)"
msgstr "%s में पहले से भाग संख्या नियत है (एक के अन्दर दूसरा अंकित विषय-सूची-संरचना)"

#: sphinx/ext/apidoc.py:86
#, python-format
msgid "Would create file %s."
msgstr "%s फाइल बन जाएगी."

#: sphinx/ext/apidoc.py:318
msgid ""
"\n"
"Look recursively in <MODULE_PATH> for Python modules and packages and create\n"
"one reST file with automodule directives per package in the <OUTPUT_PATH>.\n"
"\n"
"The <EXCLUDE_PATTERN>s can be file and/or directory patterns that will be\n"
"excluded from generation.\n"
"\n"
"Note: By default this script will not overwrite already created files."
msgstr "\n<MODULE_PATH> में पाइथन प्रभाग और पैकेज की पुनरावर्तित खोज करें और\nस्वतःप्रभाग निर्देश द्वारा <OUTPUT_PATH> में प्रति पैकेज एक रेस्ट #reST# फाइल बनाएं.\n\n<EXCLUDE_PATTERN> फाइल और/ अथवा निर्देशिका स्वरुप हो सकते हैं\nजो निर्माण प्रकिया में छोड़ दिए जाएंगे.\n\nनोट: सामान्यतया यह स्क्रिप्ट किसी पहले से बनाई गई फाइल पर पुनर्लेखन नहीं करती."

#: sphinx/ext/apidoc.py:331
msgid "path to module to document"
msgstr "प्रभाग से लेखपत्र का पथ"

#: sphinx/ext/apidoc.py:333
msgid ""
"fnmatch-style file and/or directory patterns to exclude from generation"
msgstr "fnmatch-style फाइल और/ अथवा निर्देशिका स्वरुप जो निर्माण प्रक्रिया से छोड़ने हैं"

#: sphinx/ext/apidoc.py:338
msgid "directory to place all output"
msgstr "सभी परिणामों को सहेजने के लिए निर्देशिका"

#: sphinx/ext/apidoc.py:343
msgid "maximum depth of submodules to show in the TOC (default: 4)"
msgstr "विषय-सूची में दिखाए जाने वाले उपप्रभागों की अधिकतम गहराई (मानक: 4)"

#: sphinx/ext/apidoc.py:346
msgid "overwrite existing files"
msgstr "मौजूदा फाइलों पर पुनर्लेखन करें"

#: sphinx/ext/apidoc.py:349
msgid ""
"follow symbolic links. Powerful when combined with "
"collective.recipe.omelette."
msgstr "सांकेतिक कड़ियों का अनुसरण करें. कलेक्टिव.रेसिपी.ऑमलेट के साथ प्रभावशाली. "

#: sphinx/ext/apidoc.py:352
msgid "run the script without creating files"
msgstr "फाइलों को बनाए बिना स्क्रिप्ट चलाएं "

#: sphinx/ext/apidoc.py:355
msgid "put documentation for each module on its own page"
msgstr "प्रत्येक प्रभाग के आलेख उसके अपने पृष्ठ में रखें"

#: sphinx/ext/apidoc.py:358
msgid "include \"_private\" modules"
msgstr "\"_private\" प्रभाग को सम्मिलित करें "

#: sphinx/ext/apidoc.py:360
msgid "filename of table of contents (default: modules)"
msgstr "विषय-सूची की फाइल का नाम (मानक: प्रभाग) "

#: sphinx/ext/apidoc.py:362
msgid "don't create a table of contents file"
msgstr "विषय-सूची की फाइल न बनाएं "

#: sphinx/ext/apidoc.py:365
msgid ""
"don't create headings for the module/package packages (e.g. when the "
"docstrings already contain them)"
msgstr "प्रभाग/पैकेज पैकेजों का शीर्षक न बनाएं (उदाहरणार्थ, जब डॉकस्ट्रिंग्स में यह पहले से हों) "

#: sphinx/ext/apidoc.py:370
msgid "put module documentation before submodule documentation"
msgstr " मुख्य प्रभाग के आलेख को उपप्रभाग के आलेख से पहले रखें"

#: sphinx/ext/apidoc.py:374
msgid ""
"interpret module paths according to PEP-0420 implicit namespaces "
"specification"
msgstr "प्रभाग पथ की व्याख्या 'पी.ई.पी.-0420 निहित नामराशि विवरण' के आधार पर करें "

#: sphinx/ext/apidoc.py:378
msgid "file suffix (default: rst)"
msgstr "फाइल प्रत्यय (मानक: rst)"

#: sphinx/ext/apidoc.py:380
msgid "generate a full project with sphinx-quickstart"
msgstr "स्फिंक्स-त्वरित-आरम्भ के साथ पूर्ण परियोजना उत्पन्न करें "

#: sphinx/ext/apidoc.py:383
msgid "append module_path to sys.path, used when --full is given"
msgstr "मोड्यूल_पाथ #module_path# को सिस.पाथ #sys.path# में जोड़ें, जब --full दिया जाता है तब इसका प्रयोग होता है "

#: sphinx/ext/apidoc.py:385
msgid "project name (default: root module name)"
msgstr "परियोजना का नाम (मानक: मूल प्रभाग का नाम) "

#: sphinx/ext/apidoc.py:387
msgid "project author(s), used when --full is given"
msgstr "परियोजना लेखक(गण), जब --full दिया जाता है तब इसका प्रयोग होता है "

#: sphinx/ext/apidoc.py:389
msgid "project version, used when --full is given"
msgstr "परियोजना संस्करण, जब --full दिया जाता है तब इसका प्रयोग होता है "

#: sphinx/ext/apidoc.py:391
msgid "project release, used when --full is given, defaults to --doc-version"
msgstr "परियोजना आवृत्ति, जब --full दिया जाता है तब इसका प्रयोग होता है "

#: sphinx/ext/apidoc.py:394
msgid "extension options"
msgstr "आयाम विकल्प "

#: sphinx/ext/apidoc.py:427
#, python-format
msgid "%s is not a directory."
msgstr "%s एक निर्देशिका नहीं है. "

#: sphinx/ext/autosectionlabel.py:49
#, python-format
msgid "section \"%s\" gets labeled as \"%s\""
msgstr ""

#: sphinx/ext/coverage.py:46
#, python-format
msgid "invalid regex %r in %s"
msgstr "अमान्य रेगएक्स #regex# %r, %s में "

#: sphinx/ext/coverage.py:75
#, python-format
msgid ""
"Testing of coverage in the sources finished, look at the results in "
"%(outdir)spython.txt."
msgstr "स्रोतों की व्यापकता की जांच पूरी, परिणाम %(outdir)spython.txt में देखें. "

#: sphinx/ext/coverage.py:89
#, python-format
msgid "invalid regex %r in coverage_c_regexes"
msgstr "अमान्य रेगएक्स #regex# %r, coverage_c_regexes में "

#: sphinx/ext/coverage.py:157
#, python-format
msgid "undocumented c api: %s [%s] in file %s"
msgstr ""

#: sphinx/ext/coverage.py:189
#, python-format
msgid "module %s could not be imported: %s"
msgstr "प्रभाग %s का आयत नहीं किया जा सका: %s"

#: sphinx/ext/coverage.py:340
#, python-format
msgid "undocumented python function: %s :: %s"
msgstr ""

#: sphinx/ext/coverage.py:356
#, python-format
msgid "undocumented python class: %s :: %s"
msgstr ""

#: sphinx/ext/coverage.py:369
#, python-format
msgid "undocumented python method: %s :: %s :: %s"
msgstr ""

#: sphinx/ext/doctest.py:115
#, python-format
msgid "missing '+' or '-' in '%s' option."
msgstr "'%s' विकल्प में अनुपस्थित '+' या '-'."

#: sphinx/ext/doctest.py:120
#, python-format
msgid "'%s' is not a valid option."
msgstr "'%s' एक मान्य विकल्प नहीं है."

#: sphinx/ext/doctest.py:134
#, python-format
msgid "'%s' is not a valid pyversion option"
msgstr "'%s' एक मान्य पाईवर्शन #pyversion# विकल्प नहीं है. "

#: sphinx/ext/doctest.py:220
msgid "invalid TestCode type"
msgstr "अमान्य टेस्टकोड का प्रकार "

#: sphinx/ext/doctest.py:281
#, python-format
msgid ""
"Testing of doctests in the sources finished, look at the results in "
"%(outdir)s/output.txt."
msgstr "स्रोतों में डॉकटेस्ट्स की जांच पूरी, परिणाम %(outdir)s/output.txt में देखें. "

#: sphinx/ext/doctest.py:438
#, python-format
msgid "no code/output in %s block at %s:%s"
msgstr "%s भाग में %s पर कोई निर्देश / परिणाम नहीं: %s"

#: sphinx/ext/doctest.py:526
#, python-format
msgid "ignoring invalid doctest code: %r"
msgstr "अमान्य डॉकटेस्ट निर्देश की उपेक्षा की जा रही है: %r"

#: sphinx/ext/duration.py:77
msgid ""
"====================== slowest reading durations ======================="
msgstr ""

#: sphinx/ext/extlinks.py:82
#, python-format
msgid ""
"hardcoded link %r could be replaced by an extlink (try using %r instead)"
msgstr ""

#: sphinx/ext/graphviz.py:135
msgid "Graphviz directive cannot have both content and a filename argument"
msgstr "ग्राफविज़ निर्देश में दोनों मापदंड, विषय-वस्तु और फाइल का नाम, नहीं हो सकते"

#: sphinx/ext/graphviz.py:145
#, python-format
msgid "External Graphviz file %r not found or reading it failed"
msgstr "बाहरी ग्राफविज़ फाइल %r नहीं मिली अथवा पढने में असफलता मिली"

#: sphinx/ext/graphviz.py:152
msgid "Ignoring \"graphviz\" directive without content."
msgstr "विषय-वस्तु के बिना ग्राफविज़ निर्देश की उपेक्षा की जा रही है. "

#: sphinx/ext/graphviz.py:268
#, python-format
msgid "graphviz_dot executable path must be set! %r"
msgstr ""

#: sphinx/ext/graphviz.py:303
#, python-format
msgid ""
"dot command %r cannot be run (needed for graphviz output), check the "
"graphviz_dot setting"
msgstr "डॉट निर्देश %r नहीं चलाया जा सकता (ग्राफविज़ परिणाम के लिए आवश्यक), ग्राफविज़_डॉट मान की जांच करें"

#: sphinx/ext/graphviz.py:310
#, python-format
msgid ""
"dot exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "डॉट त्रुटि के साथ बहार आ गया:\n[stderr]\n%r\n[stdout]\n%r"

#: sphinx/ext/graphviz.py:313
#, python-format
msgid ""
"dot did not produce an output file:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "डॉट ने किसी परिणाम फाइल का नहीं बनाया:\n[stderr]\n%r\n[stdout]\n%r"

#: sphinx/ext/graphviz.py:329
#, python-format
msgid "graphviz_output_format must be one of 'png', 'svg', but is %r"
msgstr "ग्राफविज़_आउटपुट_फॉर्मेट का 'पी.एन.जी', 'एस.वी.जी.', होना आवश्यक है, पर यह %r है"

#: sphinx/ext/graphviz.py:333 sphinx/ext/graphviz.py:386
#: sphinx/ext/graphviz.py:423
#, python-format
msgid "dot code %r: %s"
msgstr "डॉट निर्देश %r: %s"

#: sphinx/ext/graphviz.py:436 sphinx/ext/graphviz.py:444
#, python-format
msgid "[graph: %s]"
msgstr "[graph: %s]"

#: sphinx/ext/graphviz.py:438 sphinx/ext/graphviz.py:446
msgid "[graph]"
msgstr "[graph]"

#: sphinx/ext/imgconverter.py:39
#, python-format
msgid ""
"Unable to run the image conversion command %r. 'sphinx.ext.imgconverter' requires ImageMagick by default. Ensure it is installed, or set the 'image_converter' option to a custom conversion command.\n"
"\n"
"Traceback: %s"
msgstr ""

#: sphinx/ext/imgconverter.py:48 sphinx/ext/imgconverter.py:72
#, python-format
msgid ""
"convert exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "परिवर्तक त्रुटि के साथ बहार आ गया:\n[stderr]\n%r\n[stdout]\n%r"

#: sphinx/ext/imgconverter.py:67
#, python-format
msgid "convert command %r cannot be run, check the image_converter setting"
msgstr ""

#: sphinx/ext/imgmath.py:158
#, python-format
msgid ""
"LaTeX command %r cannot be run (needed for math display), check the "
"imgmath_latex setting"
msgstr "लाटेक्स आदेश %r नहीं चलाया जा सकता (गणित दिखाने के लिए आवश्यक). आई.एम्.जी.मैथ_लाटेक्स मान की जाँच करें"

#: sphinx/ext/imgmath.py:173
#, python-format
msgid ""
"%s command %r cannot be run (needed for math display), check the imgmath_%s "
"setting"
msgstr "%s आदेश %r नहीं चलाया जा सकता (गणित दिखाने के लिए आवश्यक). imgmath_%s मान की जाँच करें"

#: sphinx/ext/imgmath.py:327
#, python-format
msgid "display latex %r: %s"
msgstr "लाटेक्स दिखाएँ %r: %s"

#: sphinx/ext/imgmath.py:361
#, python-format
msgid "inline latex %r: %s"
msgstr "पंक्तिबद्ध लाटेक्स %r: %s"

#: sphinx/ext/imgmath.py:368 sphinx/ext/mathjax.py:53
msgid "Link to this equation"
msgstr ""

#: sphinx/ext/intersphinx.py:195
#, python-format
msgid "intersphinx inventory has moved: %s -> %s"
msgstr "इन्टरस्फिंक्स सामान स्थानांतरित हो चुका है: %s -> %s"

#: sphinx/ext/intersphinx.py:230
#, python-format
msgid "loading intersphinx inventory from %s..."
msgstr "इन्टरस्फिंक्स सामान को %s से चढ़ाया जा रहा है ..."

#: sphinx/ext/intersphinx.py:244
msgid ""
"encountered some issues with some of the inventories, but they had working "
"alternatives:"
msgstr "कुछ चीजों के साथ कुछ समस्या है, लेकिन काम के दूसरे विकल्प उपलब्ध हैं: "

#: sphinx/ext/intersphinx.py:250
msgid "failed to reach any of the inventories with the following issues:"
msgstr "कुछ चीजों पहुँचने में असफलता मिली और यह समस्याएँ मिलीं: "

#: sphinx/ext/intersphinx.py:303
#, python-format
msgid "(in %s v%s)"
msgstr "(%s v%s में)"

#: sphinx/ext/intersphinx.py:305
#, python-format
msgid "(in %s)"
msgstr "(%s में)"

#: sphinx/ext/intersphinx.py:538
#, python-format
msgid "inventory for external cross-reference not found: %r"
msgstr ""

#: sphinx/ext/intersphinx.py:546
#, python-format
msgid "invalid external cross-reference suffix: %r"
msgstr ""

#: sphinx/ext/intersphinx.py:557
#, python-format
msgid "domain for external cross-reference not found: %r"
msgstr ""

#: sphinx/ext/intersphinx.py:750
#, python-format
msgid "external %s:%s reference target not found: %s"
msgstr ""

#: sphinx/ext/intersphinx.py:775
#, python-format
msgid "intersphinx identifier %r is not string. Ignored"
msgstr "इन्टरस्फिंक्स निर्धारक %r अक्षरमाला नहीं है. उपेक्षित"

#: sphinx/ext/intersphinx.py:797
#, python-format
msgid "Failed to read intersphinx_mapping[%s], ignored: %r"
msgstr ""

#: sphinx/ext/linkcode.py:69 sphinx/ext/viewcode.py:199
msgid "[source]"
msgstr "[स्रोत]"

#: sphinx/ext/todo.py:69
msgid "Todo"
msgstr "अपूर्ण "

#: sphinx/ext/todo.py:102
#, python-format
msgid "TODO entry found: %s"
msgstr "अपूर्ण प्रविष्टि मिली: %s "

#: sphinx/ext/todo.py:161
msgid "<<original entry>>"
msgstr "<<मूल प्रविष्टि>>"

#: sphinx/ext/todo.py:163
#, python-format
msgid "(The <<original entry>> is located in %s, line %d.)"
msgstr "(<<मूल प्रविष्टि>> %s, पंक्ति %d में उपस्थित है.)"

#: sphinx/ext/todo.py:173
msgid "original entry"
msgstr "मौलिक प्रविष्टि"

#: sphinx/ext/viewcode.py:256
msgid "highlighting module code... "
msgstr "प्रभाग निर्देश विशिष्ट रूप से दर्शाया जा रहा है..."

#: sphinx/ext/viewcode.py:284
msgid "[docs]"
msgstr "[docs]"

#: sphinx/ext/viewcode.py:304
msgid "Module code"
msgstr "प्रभाग निर्देश"

#: sphinx/ext/viewcode.py:310
#, python-format
msgid "<h1>Source code for %s</h1>"
msgstr "<h1>%s का स्रोत निर्देश </h1>"

#: sphinx/ext/viewcode.py:336
msgid "Overview: module code"
msgstr "सिंहावलोकन: प्रभाग निर्देश"

#: sphinx/ext/viewcode.py:337
msgid "<h1>All modules for which code is available</h1>"
msgstr "<h1>सभी प्रभाग जिनके लिए निर्देश उपलब्ध है</h1>"

#: sphinx/ext/autodoc/__init__.py:135
#, python-format
msgid "invalid value for member-order option: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:143
#, python-format
msgid "invalid value for class-doc-from option: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:399
#, python-format
msgid "invalid signature for auto%s (%r)"
msgstr "स्वतः %s (%r) के लिए अमान्य हस्ताक्षर"

#: sphinx/ext/autodoc/__init__.py:515
#, python-format
msgid "error while formatting arguments for %s: %s"
msgstr "%s के पदों का प्रारूप बनाने में व्यवधान: %s"

#: sphinx/ext/autodoc/__init__.py:798
#, python-format
msgid ""
"autodoc: failed to determine %s.%s (%r) to be documented, the following exception was raised:\n"
"%s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:893
#, python-format
msgid ""
"don't know which module to import for autodocumenting %r (try placing a "
"\"module\" or \"currentmodule\" directive in the document, or giving an "
"explicit module name)"
msgstr "पता नहीं है कि कौन सा प्रभाग स्वतःप्रलेखन %r के लिए आयात करना है (लेखपत्र में \"प्रभाग\" या \"वर्तमान-प्रभाग\" निर्देश रख कर देखें; अथवा स्पष्ट प्रभाग नाम देकर देखें)"

#: sphinx/ext/autodoc/__init__.py:937
#, python-format
msgid "A mocked object is detected: %r"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:956
#, python-format
msgid "error while formatting signature for %s: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1019
msgid "\"::\" in automodule name doesn't make sense"
msgstr "स्वतः प्रभाग नाम में \"::\"  विवेकहीन है"

#: sphinx/ext/autodoc/__init__.py:1026
#, python-format
msgid "signature arguments or return annotation given for automodule %s"
msgstr "स्वतः-प्रभाग %s के लिए हस्ताक्षर पद अथवा प्रत्युत्तरित टिप्पणी प्रदान की गई"

#: sphinx/ext/autodoc/__init__.py:1039
#, python-format
msgid ""
"__all__ should be a list of strings, not %r (in module %s) -- ignoring "
"__all__"
msgstr "__all__ अंतिम अक्षरमाला होनी चाहिए, न कि %r (%s प्रभाग में) --  __all__ की उपेक्षा की जाएगी"

#: sphinx/ext/autodoc/__init__.py:1105
#, python-format
msgid ""
"missing attribute mentioned in :members: option: module %s, attribute %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1327 sphinx/ext/autodoc/__init__.py:1404
#: sphinx/ext/autodoc/__init__.py:2824
#, python-format
msgid "Failed to get a function signature for %s: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1618
#, python-format
msgid "Failed to get a constructor signature for %s: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1745
#, python-format
msgid "Bases: %s"
msgstr "आधार: %s"

#: sphinx/ext/autodoc/__init__.py:1759
#, python-format
msgid "missing attribute %s in object %s"
msgstr "%s गुण %s वस्तु में अनुपस्थित"

#: sphinx/ext/autodoc/__init__.py:1858 sphinx/ext/autodoc/__init__.py:1895
#: sphinx/ext/autodoc/__init__.py:1990
#, python-format
msgid "alias of %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1878
#, python-format
msgid "alias of TypeVar(%s)"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:2216 sphinx/ext/autodoc/__init__.py:2316
#, python-format
msgid "Failed to get a method signature for %s: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:2447
#, python-format
msgid "Invalid __slots__ found on %s. Ignored."
msgstr ""

#: sphinx/ext/autodoc/preserve_defaults.py:190
#, python-format
msgid "Failed to parse a default argument value for %r: %s"
msgstr ""

#: sphinx/ext/autodoc/type_comment.py:132
#, python-format
msgid "Failed to update signature for %r: parameter not found: %s"
msgstr ""

#: sphinx/ext/autodoc/type_comment.py:135
#, python-format
msgid "Failed to parse type_comment for %r: %s"
msgstr ""

#: sphinx/ext/autosummary/__init__.py:251
#, python-format
msgid "autosummary references excluded document %r. Ignored."
msgstr ""

#: sphinx/ext/autosummary/__init__.py:253
#, python-format
msgid ""
"autosummary: stub file not found %r. Check your autosummary_generate "
"setting."
msgstr ""

#: sphinx/ext/autosummary/__init__.py:272
msgid "A captioned autosummary requires :toctree: option. ignored."
msgstr ""

#: sphinx/ext/autosummary/__init__.py:325
#, python-format
msgid ""
"autosummary: failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr ""

#: sphinx/ext/autosummary/__init__.py:339
#, python-format
msgid "failed to parse name %s"
msgstr "पद-विच्छेदन में असफलता: %s"

#: sphinx/ext/autosummary/__init__.py:344
#, python-format
msgid "failed to import object %s"
msgstr "विषय-वस्तु के आयात में असफलता: %s"

#: sphinx/ext/autosummary/__init__.py:802
#, python-format
msgid "autosummary_generate: file not found: %s"
msgstr ""

#: sphinx/ext/autosummary/__init__.py:810
msgid ""
"autosummary generates .rst files internally. But your source_suffix does not"
" contain .rst. Skipped."
msgstr ""

#: sphinx/ext/autosummary/generate.py:200
#: sphinx/ext/autosummary/generate.py:358
#, python-format
msgid ""
"autosummary: failed to determine %r to be documented, the following exception was raised:\n"
"%s"
msgstr ""

#: sphinx/ext/autosummary/generate.py:470
#, python-format
msgid "[autosummary] generating autosummary for: %s"
msgstr "[ऑटोसमरी] अब इसका स्वतःसारांश बना रहा है: %s"

#: sphinx/ext/autosummary/generate.py:474
#, python-format
msgid "[autosummary] writing to %s"
msgstr "[ऑटोसमरी] %s पर लिख रहा है"

#: sphinx/ext/autosummary/generate.py:517
#, python-format
msgid ""
"[autosummary] failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr ""

#: sphinx/ext/autosummary/generate.py:690
msgid ""
"\n"
"Generate ReStructuredText using autosummary directives.\n"
"\n"
"sphinx-autogen is a frontend to sphinx.ext.autosummary.generate. It generates\n"
"the reStructuredText files from the autosummary directives contained in the\n"
"given input files.\n"
"\n"
"The format of the autosummary directive is documented in the\n"
"``sphinx.ext.autosummary`` Python module and can be read using::\n"
"\n"
"  pydoc sphinx.ext.autosummary\n"
msgstr "\nस्वतः सारांश #autosummary# निर्देश का प्रयोग करते हुए पुर्नसरंचितपाठ बनाता है.\n\nस्फिंक्स-ऑटोजेन स्फिंक्स.एक्स्ट.ऑटोसमरी.जेनेरेट का मुखड़ा है.\nयह प्रदत्त फाइलों में सम्मिलित ऑटो समरी निर्देशों के अनुसार पुर्नसरंचितपाठ बनाता है\n\nस्वतः सारांश #autosummary# निर्देश का प्रारूप स्फिंक्स.एक्स्ट.ऑटोसमरी \nपाइथन प्रभाग में निबंधित है और इसे आप निम्नलिखित माध्यम से पढ़ सकते हैं:\n\n  pydoc sphinx.ext.autosummary\n"

#: sphinx/ext/autosummary/generate.py:707
msgid "source files to generate rST files for"
msgstr "आर.एस.टी. फाइलें बनाने के लिए स्रोत फाइलें"

#: sphinx/ext/autosummary/generate.py:711
msgid "directory to place all output in"
msgstr "सभी परिणाम रखने के लिए निर्देशिका"

#: sphinx/ext/autosummary/generate.py:714
#, python-format
msgid "default suffix for files (default: %(default)s)"
msgstr "फाइलों के लिए मानक प्रत्यय (मानक: %(default)s)"

#: sphinx/ext/autosummary/generate.py:718
#, python-format
msgid "custom template directory (default: %(default)s)"
msgstr "पारंपरिक प्रारूप निर्देशिका (मानक: %(default)s)"

#: sphinx/ext/autosummary/generate.py:722
#, python-format
msgid "document imported members (default: %(default)s)"
msgstr "लेखपत्र आयातित सदस्य (मानक: %(default)s)"

#: sphinx/ext/autosummary/generate.py:726
#, python-format
msgid ""
"document exactly the members in module __all__ attribute. (default: "
"%(default)s)"
msgstr ""

#: sphinx/ext/napoleon/__init__.py:341 sphinx/ext/napoleon/docstring.py:728
msgid "Keyword Arguments"
msgstr "मुख्य शब्दों के चर-पद"

#: sphinx/ext/napoleon/docstring.py:682
msgid "Example"
msgstr "उदाहरण"

#: sphinx/ext/napoleon/docstring.py:683
msgid "Examples"
msgstr "कुछ उदाहरण"

#: sphinx/ext/napoleon/docstring.py:744
msgid "Notes"
msgstr "टिप्पणियाँ"

#: sphinx/ext/napoleon/docstring.py:753
msgid "Other Parameters"
msgstr "अन्य मापदण्ड"

#: sphinx/ext/napoleon/docstring.py:789
msgid "Receives"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:793
msgid "References"
msgstr "सन्दर्भ"

#: sphinx/ext/napoleon/docstring.py:825
msgid "Warns"
msgstr "चेतावनी देता है"

#: sphinx/ext/napoleon/docstring.py:829
msgid "Yields"
msgstr "मिलता है"

#: sphinx/ext/napoleon/docstring.py:987
#, python-format
msgid "invalid value set (missing closing brace): %s"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:994
#, python-format
msgid "invalid value set (missing opening brace): %s"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:1001
#, python-format
msgid "malformed string literal (missing closing quote): %s"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:1008
#, python-format
msgid "malformed string literal (missing opening quote): %s"
msgstr ""

#: sphinx/locale/__init__.py:228
msgid "Attention"
msgstr "सावधानी"

#: sphinx/locale/__init__.py:229
msgid "Caution"
msgstr "चेतावनी"

#: sphinx/locale/__init__.py:230
msgid "Danger"
msgstr "खतरा"

#: sphinx/locale/__init__.py:231
msgid "Error"
msgstr "गलती"

#: sphinx/locale/__init__.py:232
msgid "Hint"
msgstr "संकेत"

#: sphinx/locale/__init__.py:233
msgid "Important"
msgstr "महत्त्वपूर्ण"

#: sphinx/locale/__init__.py:234
msgid "Note"
msgstr "टिप्पणी "

#: sphinx/locale/__init__.py:235
msgid "See also"
msgstr "यह भी देखिए"

#: sphinx/locale/__init__.py:236
msgid "Tip"
msgstr "सलाह"

#: sphinx/locale/__init__.py:237
msgid "Warning"
msgstr "चेतावनी"

#: sphinx/templates/latex/longtable.tex_t:52
#: sphinx/templates/latex/sphinxmessages.sty_t:8
msgid "continued from previous page"
msgstr "पिछले पृष्ठ से जारी"

#: sphinx/templates/latex/longtable.tex_t:63
#: sphinx/templates/latex/sphinxmessages.sty_t:9
msgid "continues on next page"
msgstr "अगले पृष्ठ पर जारी"

#: sphinx/templates/latex/sphinxmessages.sty_t:10
msgid "Non-alphabetical"
msgstr "अकारादि-क्रमहीन "

#: sphinx/templates/latex/sphinxmessages.sty_t:12
msgid "Numbers"
msgstr "संख्याएं "

#: sphinx/templates/latex/sphinxmessages.sty_t:13
msgid "page"
msgstr "पृष्ठ"

#: sphinx/themes/agogo/layout.html:38 sphinx/themes/basic/globaltoc.html:10
#: sphinx/themes/basic/localtoc.html:12 sphinx/themes/scrolls/layout.html:41
msgid "Table of Contents"
msgstr "विषय-सूची"

#: sphinx/themes/agogo/layout.html:43 sphinx/themes/basic/layout.html:141
#: sphinx/themes/basic/search.html:11 sphinx/themes/basic/search.html:23
msgid "Search"
msgstr "खोज"

#: sphinx/themes/agogo/layout.html:46 sphinx/themes/basic/searchbox.html:16
#: sphinx/themes/basic/searchfield.html:18
msgid "Go"
msgstr "चलिए"

#: sphinx/themes/agogo/layout.html:90 sphinx/themes/basic/sourcelink.html:15
msgid "Show Source"
msgstr "स्रोत दिखाएँ"

#: sphinx/themes/basic/defindex.html:11
msgid "Overview"
msgstr "सिंहावलोकन"

#: sphinx/themes/basic/defindex.html:15
msgid "Welcome! This is"
msgstr "नमस्ते! यह है"

#: sphinx/themes/basic/defindex.html:16
msgid "the documentation for"
msgstr "आलेख विषय"

#: sphinx/themes/basic/defindex.html:17
msgid "last updated"
msgstr "अंतिम परिवर्धन"

#: sphinx/themes/basic/defindex.html:20
msgid "Indices and tables:"
msgstr "सूचियाँ और सारणियाँ:"

#: sphinx/themes/basic/defindex.html:23
msgid "Complete Table of Contents"
msgstr "विस्तृत विषय-सूची"

#: sphinx/themes/basic/defindex.html:24
msgid "lists all sections and subsections"
msgstr "सभी अनुभागों एवं उप-अनुभागों की सूची"

#: sphinx/themes/basic/defindex.html:26
msgid "search this documentation"
msgstr "इस आलेख में खोजें"

#: sphinx/themes/basic/defindex.html:28
msgid "Global Module Index"
msgstr "सार्वभौमिक प्रभाग सूची"

#: sphinx/themes/basic/defindex.html:29
msgid "quick access to all modules"
msgstr "सभी प्रभाग तक तुरंत पहुँच"

#: sphinx/themes/basic/defindex.html:31
msgid "all functions, classes, terms"
msgstr "सभी कार्ययुक्तियां, वर्ग, शब्द"

#: sphinx/themes/basic/genindex-single.html:33
#, python-format
msgid "Index &ndash; %(key)s"
msgstr "अनुक्रमणिका &ndash; %(key)s"

#: sphinx/themes/basic/genindex-single.html:61
#: sphinx/themes/basic/genindex-split.html:24
#: sphinx/themes/basic/genindex-split.html:38
#: sphinx/themes/basic/genindex.html:73
msgid "Full index on one page"
msgstr "एक पृष्ठ पर पूरी अनुक्रमणिका"

#: sphinx/themes/basic/genindex-split.html:16
msgid "Index pages by letter"
msgstr "अक्षर द्वारा अनुक्रमित पृष्ठ"

#: sphinx/themes/basic/genindex-split.html:25
msgid "can be huge"
msgstr "बृहदाकार हो सकता है"

#: sphinx/themes/basic/layout.html:26
msgid "Navigation"
msgstr "संचालन"

#: sphinx/themes/basic/layout.html:126
#, python-format
msgid "Search within %(docstitle)s"
msgstr "%(docstitle)s में खोजें"

#: sphinx/themes/basic/layout.html:135
msgid "About these documents"
msgstr "इन लेखपत्रों के बारे में"

#: sphinx/themes/basic/layout.html:144 sphinx/themes/basic/layout.html:188
#: sphinx/themes/basic/layout.html:190
msgid "Copyright"
msgstr "सर्वाधिकार"

#: sphinx/themes/basic/layout.html:194 sphinx/themes/basic/layout.html:200
#, python-format
msgid "&#169; %(copyright_prefix)s %(copyright)s."
msgstr ""

#: sphinx/themes/basic/layout.html:212
#, python-format
msgid "Last updated on %(last_updated)s."
msgstr "अंतिम बार सम्पादित %(last_updated)s."

#: sphinx/themes/basic/layout.html:215
#, python-format
msgid ""
"Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> "
"%(sphinx_version)s."
msgstr ""

#: sphinx/themes/basic/opensearch.xml:4
#, python-format
msgid "Search %(docstitle)s"
msgstr " %(docstitle)s में खोजें"

#: sphinx/themes/basic/relations.html:12
msgid "Previous topic"
msgstr "पिछला प्रकरण"

#: sphinx/themes/basic/relations.html:14
msgid "previous chapter"
msgstr "पिछला अध्याय"

#: sphinx/themes/basic/relations.html:19
msgid "Next topic"
msgstr "अगला प्रकरण"

#: sphinx/themes/basic/relations.html:21
msgid "next chapter"
msgstr "अगला अध्याय"

#: sphinx/themes/basic/search.html:28
msgid ""
"Please activate JavaScript to enable the search\n"
"    functionality."
msgstr "खोज कार्य के लिए जावा स्क्रिप्ट का होना आवश्यक है. कृपया जावा स्क्रिप्ट को शुरू करें."

#: sphinx/themes/basic/search.html:36
msgid ""
"Searching for multiple words only shows matches that contain\n"
"    all words."
msgstr ""

#: sphinx/themes/basic/search.html:43
msgid "search"
msgstr "खोज"

#: sphinx/themes/basic/searchbox.html:12
msgid "Quick search"
msgstr "त्वरित खोज"

#: sphinx/themes/basic/sourcelink.html:12
msgid "This Page"
msgstr "यह पृष्ठ "

#: sphinx/themes/basic/changes/frameset.html:5
#: sphinx/themes/basic/changes/versionchanges.html:12
#, python-format
msgid "Changes in Version %(version)s &#8212; %(docstitle)s"
msgstr "परिवर्तित संस्करण %(version)s &#8212; %(docstitle)s"

#: sphinx/themes/basic/changes/rstsource.html:5
#, python-format
msgid "%(filename)s &#8212; %(docstitle)s"
msgstr "%(filename)s &#8212; %(docstitle)s"

#: sphinx/themes/basic/changes/versionchanges.html:17
#, python-format
msgid "Automatically generated list of changes in version %(version)s"
msgstr "संस्करण %(version)s में स्वतः रचित परिवर्तनों की सूची"

#: sphinx/themes/basic/changes/versionchanges.html:18
msgid "Library changes"
msgstr "पुस्तकालय में परिवर्तन"

#: sphinx/themes/basic/changes/versionchanges.html:23
msgid "C API changes"
msgstr "सी ऐ.पी.आई. परिवर्तन"

#: sphinx/themes/basic/changes/versionchanges.html:25
msgid "Other changes"
msgstr "अन्य परिवर्तन"

#: sphinx/themes/basic/static/searchtools.js:112
msgid "Search Results"
msgstr "खोज परीणाम "

#: sphinx/themes/basic/static/searchtools.js:114
msgid ""
"Your search did not match any documents. Please make sure that all words are"
" spelled correctly and that you've selected enough categories."
msgstr "आपके खोज परिणामों में कोई प्रलेख नहीं मिला. कृपया सुनिश्चित करें कि सभी शब्दों की वर्तनी शुद्ध है और आपने यथेष्ट श्रेणियां चुनी हैं."

#: sphinx/themes/basic/static/searchtools.js:118
msgid ""
"Search finished, found ${resultCount} page(s) matching the search query."
msgstr ""

#: sphinx/themes/basic/static/searchtools.js:246
msgid "Searching"
msgstr "खोज जारी"

#: sphinx/themes/basic/static/searchtools.js:262
msgid "Preparing search..."
msgstr "खोज की तैयारी"

#: sphinx/themes/basic/static/searchtools.js:463
msgid ", in "
msgstr ", में "

#: sphinx/themes/basic/static/sphinx_highlight.js:112
msgid "Hide Search Matches"
msgstr "खोजे गए जोड़े छिपाएं"

#: sphinx/themes/classic/layout.html:20
#: sphinx/themes/classic/static/sidebar.js_t:57
msgid "Collapse sidebar"
msgstr "किनारे का स्थान घटाएं"

#: sphinx/themes/classic/static/sidebar.js_t:48
msgid "Expand sidebar"
msgstr "किनारे का स्थान बढ़ाएं"

#: sphinx/themes/haiku/layout.html:24
msgid "Contents"
msgstr "विषय सामिग्री"

#: sphinx/transforms/__init__.py:128
msgid "could not calculate translation progress!"
msgstr ""

#: sphinx/transforms/__init__.py:133
msgid "no translated elements!"
msgstr ""

#: sphinx/transforms/__init__.py:250
#, python-format
msgid ""
"4 column based index found. It might be a bug of extensions you use: %r"
msgstr "4 पंक्तिबद्ध सूचियाँ मिलीं. यह आपके द्वारा उपयोग किए गए आयाम की त्रुटि हो सकती है: %r"

#: sphinx/transforms/__init__.py:291
#, python-format
msgid "Footnote [%s] is not referenced."
msgstr "पाद-टिप्पणी [%s] का कोई सन्दर्भ नहीं है."

#: sphinx/transforms/__init__.py:297
msgid "Footnote [#] is not referenced."
msgstr "पाद-टिप्पणी [#] सन्दर्भ कहीं नहीं है"

#: sphinx/transforms/i18n.py:205 sphinx/transforms/i18n.py:270
msgid ""
"inconsistent footnote references in translated message. original: {0}, "
"translated: {1}"
msgstr "अनुवादित संदेश में असंगत पाद-टिप्पणी के प्रसंग. मूल: {0}, अनुवादित: {1}"

#: sphinx/transforms/i18n.py:245
msgid ""
"inconsistent references in translated message. original: {0}, translated: "
"{1}"
msgstr "अनुवादित संदेश में असंगत प्रसंग. मूल: {0}, अनुवादित: {1}"

#: sphinx/transforms/i18n.py:285
msgid ""
"inconsistent citation references in translated message. original: {0}, "
"translated: {1}"
msgstr "अनुवादित संदेश में असंगत उद्धरण के प्रसंग. मूल: {0}, अनुवादित: {1}"

#: sphinx/transforms/i18n.py:302
msgid ""
"inconsistent term references in translated message. original: {0}, "
"translated: {1}"
msgstr "अनुवादित संदेश में असंगत शब्द के प्रसंग. मूल: {0}, अनुवादित: {1}"

#: sphinx/transforms/post_transforms/__init__.py:116
msgid ""
"Could not determine the fallback text for the cross-reference. Might be a "
"bug."
msgstr ""

#: sphinx/transforms/post_transforms/__init__.py:158
#, python-format
msgid "more than one target found for 'any' cross-reference %r: could be %s"
msgstr "किसी भी पारस्परिक-सन्दर्भ के लिए एक से अधिक लक्ष्य मिले %r: %s संभव"

#: sphinx/transforms/post_transforms/__init__.py:209
#, python-format
msgid "%s:%s reference target not found: %s"
msgstr ""

#: sphinx/transforms/post_transforms/__init__.py:212
#, python-format
msgid "%r reference target not found: %s"
msgstr ""

#: sphinx/transforms/post_transforms/images.py:89
#, python-format
msgid "Could not fetch remote image: %s [%d]"
msgstr "दूरस्थ चित्र नहीं लाया जा सका: %s [%d]"

#: sphinx/transforms/post_transforms/images.py:117
#, python-format
msgid "Could not fetch remote image: %s [%s]"
msgstr "दूरस्थ चित्र नहीं लाया जा सका: %s [%s]"

#: sphinx/transforms/post_transforms/images.py:135
#, python-format
msgid "Unknown image format: %s..."
msgstr "अज्ञात चित्र प्रारूप: %s..."

#: sphinx/util/__init__.py:168
#, python-format
msgid "undecodable source characters, replacing with \"?\": %r"
msgstr "असाधनीय स्रोत अक्षर, \"?\" द्वारा बदले जा रहे हैं: %r"

#: sphinx/util/display.py:78
msgid "skipped"
msgstr "छोड़ा "

#: sphinx/util/display.py:83
msgid "failed"
msgstr "असफल"

#: sphinx/util/docfields.py:87
#, python-format
msgid ""
"Problem in %s domain: field is supposed to use role '%s', but that role is "
"not in the domain."
msgstr ""

#: sphinx/util/docutils.py:295
#, python-format
msgid "unknown directive or role name: %s:%s"
msgstr ""

#: sphinx/util/docutils.py:591
#, python-format
msgid "unknown node type: %r"
msgstr "अज्ञात बिंदु प्रकार: %r"

#: sphinx/util/i18n.py:94
#, python-format
msgid "reading error: %s, %s"
msgstr "अशुद्धि पाठन: %s, %s"

#: sphinx/util/i18n.py:101
#, python-format
msgid "writing error: %s, %s"
msgstr "अशुद्धि लेखन: %s, %s"

#: sphinx/util/i18n.py:125
#, python-format
msgid "locale_dir %s does not exist"
msgstr ""

#: sphinx/util/i18n.py:215
#, python-format
msgid ""
"Invalid date format. Quote the string by single quote if you want to output "
"it directly: %s"
msgstr "अमान्य तिथि प्रारूप. यदि आप सीधे परिणाम में दर्शाना चाहते हैं तो अक्षरमाला को एकाकी उद्धरण चिन्ह द्वारा चिन्हित करें: %s"

#: sphinx/util/nodes.py:386
#, python-format
msgid ""
"%r is deprecated for index entries (from entry %r). Use 'pair: %s' instead."
msgstr ""

#: sphinx/util/nodes.py:439
#, python-format
msgid "toctree contains ref to nonexisting file %r"
msgstr "विषय-सूची-संरचना में अविद्यमान फाइल %r का सन्दर्भ है"

#: sphinx/util/nodes.py:637
#, python-format
msgid "exception while evaluating only directive expression: %s"
msgstr "केवल निर्देशक भाव का मूल्यांकन करते समय अपवाद: %s"

#: sphinx/util/rst.py:71
#, python-format
msgid "default role %s not found"
msgstr "मानक भूमिका '%s' नहीं मिली"

#: sphinx/writers/html5.py:100 sphinx/writers/html5.py:109
msgid "Link to this definition"
msgstr ""

#: sphinx/writers/html5.py:397
#, python-format
msgid "numfig_format is not defined for %s"
msgstr "%s के लिए नमफिग_फॉर्मेट नहीं बताया गया है"

#: sphinx/writers/html5.py:407
#, python-format
msgid "Any IDs not assigned for %s node"
msgstr "%s बिंदु के लिए कोई पहचान-चिन्ह नहीं दिया गया"

#: sphinx/writers/html5.py:462
msgid "Link to this term"
msgstr ""

#: sphinx/writers/html5.py:496 sphinx/writers/html5.py:501
msgid "Link to this heading"
msgstr ""

#: sphinx/writers/html5.py:505
msgid "Link to this table"
msgstr ""

#: sphinx/writers/html5.py:548
msgid "Link to this code"
msgstr ""

#: sphinx/writers/html5.py:550
msgid "Link to this image"
msgstr ""

#: sphinx/writers/html5.py:552
msgid "Link to this toctree"
msgstr ""

#: sphinx/writers/html5.py:688
msgid "Could not obtain image size. :scale: option is ignored."
msgstr "चित्र का आकार नहीं मिल सका. :scale: विकल्प की उपेक्षा की जा रही है."

#: sphinx/writers/latex.py:335
#, python-format
msgid "unknown %r toplevel_sectioning for class %r"
msgstr "अज्ञात %r उच्चतमस्तर_विभाजन #toplevel_sectioning# %r वर्ग के लिए"

#: sphinx/writers/latex.py:386
msgid "too large :maxdepth:, ignored."
msgstr "अत्याधिक अधिकतम गहराई # :maxdepth: #, उपेक्षित किया गया."

#: sphinx/writers/latex.py:625
msgid "document title is not a single Text node"
msgstr "लेखपत्र का शीर्षक एकल पाठ बिंदु नहीं है"

#: sphinx/writers/latex.py:656 sphinx/writers/texinfo.py:626
msgid ""
"encountered title node not in section, topic, table, admonition or sidebar"
msgstr "पाया गया शीर्ष बिंदु किसी भाग, प्रसंग, तालिका, विषय-प्रबोध अथवा पार्श्व-स्थान में नहीं है"

#: sphinx/writers/latex.py:959 sphinx/writers/manpage.py:259
#: sphinx/writers/texinfo.py:641
msgid "Footnotes"
msgstr "पाद टिप्पणियां"

#: sphinx/writers/latex.py:1028
msgid ""
"both tabularcolumns and :widths: option are given. :widths: is ignored."
msgstr "दोनों तालिका-स्तंभ और :चौड़ाई: विकल्प दिए गए हैं. :चौड़ाई:  मान की उपेक्षा की जाएगी."

#: sphinx/writers/latex.py:1388
#, python-format
msgid "dimension unit %s is invalid. Ignored."
msgstr "परिमाण मात्रक %s अमान्य है. उपेक्षा की जाएगी."

#: sphinx/writers/latex.py:1722
#, python-format
msgid "unknown index entry type %s found"
msgstr "अनुक्रमणिका की प्रविष्टि का प्रकार %s मिला"

#: sphinx/writers/manpage.py:305 sphinx/writers/text.py:907
#, python-format
msgid "[image: %s]"
msgstr "[चित्र: %s]"

#: sphinx/writers/manpage.py:306 sphinx/writers/text.py:908
msgid "[image]"
msgstr "[चित्र]"

#: sphinx/writers/texinfo.py:1197
msgid "caption not inside a figure."
msgstr "शीर्षक रेखाचित्र के भीतर नहीं है"

#: sphinx/writers/texinfo.py:1284
#, python-format
msgid "unimplemented node type: %r"
msgstr "अकार्यान्वित बिंदु प्रकार: %r"
