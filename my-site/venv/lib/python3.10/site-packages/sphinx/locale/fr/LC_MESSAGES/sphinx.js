Documentation.addTranslations({
    "locale": "fr",
    "messages": {
        "%(filename)s &#8212; %(docstitle)s": "%(filename)s &#8212; %(docstitle)s",
        "&#169; %(copyright_prefix)s %(copyright)s.": "&#169; %(copyright_prefix)s %(copyright)s.",
        ", in ": ", dans ",
        "About these documents": "\u00c0 propos de ces documents",
        "Automatically generated list of changes in version %(version)s": "Liste auto-g\u00e9n\u00e9r\u00e9e des modifications dans la version %(version)s",
        "C API changes": "Modifications de l'API C",
        "Changes in Version %(version)s &#8212; %(docstitle)s": "Changements dans la version %(version)s &#8212; %(docstitle)s",
        "Collapse sidebar": "R\u00e9duire la barre lat\u00e9rale",
        "Complete Table of Contents": "Table des mati\u00e8res compl\u00e8te",
        "Contents": "Contenu",
        "Copyright": "Copyright",
        "Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> %(sphinx_version)s.": "Cr\u00e9\u00e9 en utilisant <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> %(sphinx_version)s.",
        "Expand sidebar": "Agrandir la barre lat\u00e9rale",
        "Full index on one page": "Index complet sur une seule page",
        "General Index": "Index g\u00e9n\u00e9ral",
        "Global Module Index": "Index g\u00e9n\u00e9ral des modules",
        "Go": "Go",
        "Hide Search Matches": "Cacher les r\u00e9sultats de la recherche",
        "Index": "Index",
        "Index &ndash; %(key)s": "Index &ndash; %(key)s",
        "Index pages by letter": "Indexer les pages par lettre",
        "Indices and tables:": "Index et tables :",
        "Last updated on %(last_updated)s.": "Mis \u00e0 jour le %(last_updated)s.",
        "Library changes": "Modifications de la biblioth\u00e8que",
        "Navigation": "Navigation",
        "Next topic": "Sujet suivant",
        "Other changes": "Autres modifications",
        "Overview": "R\u00e9sum\u00e9",
        "Please activate JavaScript to enable the search\n    functionality.": "Veuillez activer le JavaScript pour que la recherche fonctionne.",
        "Preparing search...": "Pr\u00e9paration de la recherche...",
        "Previous topic": "Sujet pr\u00e9c\u00e9dent",
        "Quick search": "Recherche rapide",
        "Search": "Recherche",
        "Search Page": "Page de recherche",
        "Search Results": "R\u00e9sultats de la recherche",
        "Search finished, found ${resultCount} page(s) matching the search query.": "Recherche termin\u00e9e, ${resultCount} page(s) correspondant \u00e0 la requ\u00eate de recherche ont \u00e9t\u00e9 trouv\u00e9es.",
        "Search within %(docstitle)s": "Recherchez dans %(docstitle)s",
        "Searching": "Recherche en cours",
        "Searching for multiple words only shows matches that contain\n    all words.": "Une recherche sur plusieurs mots ne retourne que les r\u00e9sultats contenant tous les mots.",
        "Show Source": "Montrer le code source",
        "Table of Contents": "Table des mati\u00e8res",
        "This Page": "Cette page",
        "Welcome! This is": "Bienvenue ! Ceci est",
        "Your search did not match any documents. Please make sure that all words are spelled correctly and that you've selected enough categories.": "Votre recherche ne correspond \u00e0 aucun document. Veuillez v\u00e9rifier que les mots sont correctement orthographi\u00e9s et que vous avez s\u00e9lectionn\u00e9 assez de cat\u00e9gories.",
        "all functions, classes, terms": "toutes les fonctions, classes, termes",
        "can be huge": "peut \u00eatre \u00e9norme",
        "last updated": "derni\u00e8re modification",
        "lists all sections and subsections": "lister l'ensemble des sections et sous-sections",
        "next chapter": "Chapitre suivant",
        "previous chapter": "Chapitre pr\u00e9c\u00e9dent",
        "quick access to all modules": "acc\u00e8s rapide \u00e0 l'ensemble des modules",
        "search": "rechercher",
        "search this documentation": "rechercher dans cette documentation",
        "the documentation for": "la documentation pour"
    },
    "plural_expr": "(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2"
});