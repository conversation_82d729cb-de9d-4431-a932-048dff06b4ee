/*
 * sphinxdoc.css_t
 * ~~~~~~~~~~~~~~~
 *
 * Sphinx stylesheet -- sphinxdoc theme.  Originally created by
 * <PERSON><PERSON> for Werkzeug.
 *
 * :copyright: Copyright 2007-2024 by the Sphinx team, see AUTHORS.
 * :license: BSD, see LICENSE for details.
 *
 */

@import url("basic.css");

/* -- page layout ----------------------------------------------------------- */

body {
    font-family: 'Lucida Grande', 'Lucida Sans Unicode', 'Geneva',
                 'Verdana', sans-serif;
    font-size: 14px;
    letter-spacing: -0.01em;
    line-height: 150%;
    text-align: center;
    background-color: #BFD1D4;
    color: black;
    padding: 0;
    border: 1px solid #aaa;

    margin: 0px 80px 0px 80px;
    min-width: 740px;
}

div.document {
    background-color: white;
    text-align: left;
    background-image: url(contents.png);
    background-repeat: repeat-x;
}

div.documentwrapper {
    float: left;
    width: 100%;
}

div.bodywrapper {
    margin: 0 calc({{ theme_sidebarwidth|todim }} + 10px) 0 0;
    border-right: 1px solid #ccc;
}

div.body {
    margin: 0;
    padding: 0.5em 20px 20px 20px;
}

div.related {
    font-size: 1em;
}

div.related ul {
    background-image: url(navigation.png);
    height: 2em;
    border-top: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
}

div.related ul li {
    margin: 0;
    padding: 0;
    height: 2em;
    float: left;
}

div.related ul li.right {
    float: right;
    margin-right: 5px;
}

div.related ul li a {
    margin: 0;
    padding: 0 5px 0 5px;
    line-height: 1.75em;
    color: #EE9816;
}

div.related ul li a:hover {
    color: #3CA8E7;
}

div.sphinxsidebarwrapper {
    padding: 0;
}

div.sphinxsidebar {
    padding: 0.5em 15px 15px 0;
    width: calc({{ theme_sidebarwidth|todim }} - 20px);
    float: right;
    font-size: 1em;
    text-align: left;
}

div.sphinxsidebar h3, div.sphinxsidebar h4 {
    margin: 1em 0 0.5em 0;
    font-size: 1em;
    padding: 0.1em 0 0.1em 0.5em;
    color: white;
    border: 1px solid #86989B;
    background-color: #AFC1C4;
}

div.sphinxsidebar h3 a {
    color: white;
}

div.sphinxsidebar ul {
    padding-left: 1.5em;
    margin-top: 7px;
    padding: 0;
    line-height: 130%;
}

div.sphinxsidebar ul ul {
    margin-left: 20px;
}

div.footer {
    background-color: #E3EFF1;
    color: #86989B;
    padding: 3px 8px 3px 0;
    clear: both;
    font-size: 0.8em;
    text-align: right;
}

div.footer a {
    color: #86989B;
    text-decoration: underline;
}

/* -- body styles ----------------------------------------------------------- */

p {
    margin: 0.8em 0 0.5em 0;
}

a {
    color: #CA7900;
    text-decoration: none;
}

a:hover {
    color: #2491CF;
}

a:visited {
    color: #551A8B;
}

div.body a {
    text-decoration: underline;
}

h1 {
    margin: 0;
    padding: 0.7em 0 0.3em 0;
    font-size: 1.5em;
    color: #11557C;
}

h2 {
    margin: 1.3em 0 0.2em 0;
    font-size: 1.35em;
    padding: 0;
}

h3 {
    margin: 1em 0 -0.3em 0;
    font-size: 1.2em;
}

div.body h1 a, div.body h2 a, div.body h3 a, div.body h4 a, div.body h5 a, div.body h6 a {
    color: black!important;
}

h1 a.anchor, h2 a.anchor, h3 a.anchor, h4 a.anchor, h5 a.anchor, h6 a.anchor {
    display: none;
    margin: 0 0 0 0.3em;
    padding: 0 0.2em 0 0.2em;
    color: #aaa!important;
}

h1:hover a.anchor, h2:hover a.anchor, h3:hover a.anchor, h4:hover a.anchor,
h5:hover a.anchor, h6:hover a.anchor {
    display: inline;
}

h1 a.anchor:hover, h2 a.anchor:hover, h3 a.anchor:hover, h4 a.anchor:hover,
h5 a.anchor:hover, h6 a.anchor:hover {
    color: #777;
    background-color: #eee;
}

a.headerlink {
    color: #c60f0f!important;
    font-size: 1em;
    margin-left: 6px;
    padding: 0 4px 0 4px;
    text-decoration: none!important;
}

a.headerlink:hover {
    background-color: #ccc;
    color: white!important;
}

cite, code, code {
    font-family: 'Consolas', 'Deja Vu Sans Mono',
                 'Bitstream Vera Sans Mono', monospace;
    font-size: 0.95em;
    letter-spacing: 0.01em;
}

code {
    background-color: #f2f2f2;
    border-bottom: 1px solid #ddd;
    color: #333;
}

code.descname, code.descclassname, code.xref {
    border: 0;
}

hr {
    border: 1px solid #abc;
    margin: 2em;
}

a code {
    border: 0;
    color: #CA7900;
}

a code:hover {
    color: #2491CF;
}

pre {
    font-family: 'Consolas', 'Deja Vu Sans Mono',
                 'Bitstream Vera Sans Mono', monospace;
    font-size: 0.95em;
    letter-spacing: 0.015em;
    line-height: 120%;
    padding: 0.5em;
    border: 1px solid #ccc;
}

pre a {
    color: inherit;
    text-decoration: underline;
}

td.linenos pre {
    padding: 0.5em 0;
}

div.quotebar {
    background-color: #f8f8f8;
    max-width: 250px;
    float: right;
    padding: 2px 7px;
    border: 1px solid #ccc;
}

nav.contents,
aside.topic,
div.topic {
    background-color: #f8f8f8;
}

table {
    border-collapse: collapse;
    margin: 0 -0.5em 0 -0.5em;
}

table td, table th {
    padding: 0.2em 0.5em 0.2em 0.5em;
}

div.admonition, div.warning {
    font-size: 0.9em;
    margin: 1em 0 1em 0;
    border: 1px solid #86989B;
    background-color: #f7f7f7;
    padding: 0;
}

div.admonition p, div.warning p {
    margin: 0.5em 1em 0.5em 1em;
    padding: 0;
}

div.admonition pre, div.warning pre {
    margin: 0.4em 1em 0.4em 1em;
}

div.admonition p.admonition-title,
div.warning p.admonition-title {
    margin: 0;
    padding: 0.1em 0 0.1em 0.5em;
    color: white;
    border-bottom: 1px solid #86989B;
    font-weight: bold;
    background-color: #AFC1C4;
}

div.warning {
    border: 1px solid #940000;
}

div.warning p.admonition-title {
    background-color: #CF0000;
    border-bottom-color: #940000;
}

div.admonition ul, div.admonition ol,
div.warning ul, div.warning ol {
    margin: 0.1em 0.5em 0.5em 3em;
    padding: 0;
}

div.versioninfo {
    margin: 1em 0 0 0;
    border: 1px solid #ccc;
    background-color: #DDEAF0;
    padding: 8px;
    line-height: 1.3em;
    font-size: 0.9em;
}

.viewcode-back {
    font-family: 'Lucida Grande', 'Lucida Sans Unicode', 'Geneva',
                 'Verdana', sans-serif;
}

div.viewcode-block:target {
    background-color: #f4debf;
    border-top: 1px solid #ac9;
    border-bottom: 1px solid #ac9;
}

div.code-block-caption {
    background-color: #ddd;
    color: #222;
    border: 1px solid #ccc;
}
