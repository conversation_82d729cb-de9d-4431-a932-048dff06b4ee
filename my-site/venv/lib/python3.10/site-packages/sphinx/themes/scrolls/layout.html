{#
    scrolls/layout.html
    ~~~~~~~~~~~~~~~~~~~

    Sphinx layout template for the scrolls theme, originally written
    by <PERSON><PERSON>.

    :copyright: Copyright 2007-2024 by the Sphinx team, see AUTHORS.
    :license: BSD, see LICENSE for details.
#}
{%- extends "basic/layout.html" %}
{%- block css %}
    {{ super() }}
    <link rel="stylesheet" href="_static/print.css" type="text/css" />
{%- endblock %}
{%- block scripts %}
    {{ super() }}
    <script src="{{ pathto('_static/theme_extras.js', 1) }}"></script>
{%- endblock %}
{# do not display relbars #}
{% block relbar1 %}{% endblock %}
{% block relbar2 %}{% endblock %}
{% block content %}
    <div id="content">
      <div class="header">
        <h1 class="heading"><a href="{{ pathto(root_doc)|e }}"
          title="back to the documentation overview"><span>{{ title|striptags|e }}</span></a></h1>
      </div>
      <div class="relnav" role="navigation" aria-label="related navigation">
        {%- if prev %}
        <a href="{{ prev.link|e }}">&laquo; {{ prev.title }}</a> |
        {%- endif %}
        <a href="{{ pathto(current_page_name)|e if current_page_name else '#' }}">{{ title }}</a>
        {%- if next %}
        | <a href="{{ next.link|e }}">{{ next.title }} &raquo;</a>
        {%- endif %}
      </div>
      <div id="contentwrapper">
        {%- if display_toc %}
        <div id="toc" role="navigation" aria-label="table of contents navigation">
          <h3>{{ _('Table of Contents') }}</h3>
          {{ toc }}
        </div>
        {%- endif %}
        <div role="main">
        {% block body %}{% endblock %}
        </div>
      </div>
    </div>
{% endblock %}
