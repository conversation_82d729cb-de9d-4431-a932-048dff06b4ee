%% NOTICES AND ADMONITIONS
%
% change this info string if making any custom modification
\ProvidesFile{sphinxlatexadmonitions.sty}[2023/03/19 admonitions]

% Provides support for this output mark-up from Sphinx latex writer:
%
% - sphinxseealso environment added at 6.1.0
%
% - sphinxadmonition (environment)
%   This is a dispatch supporting
%
%   - note, hint, important, tip                 (via sphinxlightbox)
%                    (also optionally via sphinxheavybox since 6.2.0)
%   - warning, caution, attention, danger, error (via sphinxheavybox)
%
%   Each sphinx<notice name> environment can be redefined by user.
%   The defaults are customizable via various colour and dimension
%   settings, cf sphinx docs (latex customization).
%
% Requires:
\RequirePackage{sphinxpackageboxes}
\RequirePackage{framed}% used by sphinxheavybox
%
% Dependencies (they do not need to be defined at time of loading):
%
% - of course the various colour and dimension options handled via sphinx.sty
%
% - dimension register \spx@image@maxheight from sphinxlatexgraphics.sty
%
% - \savenotes/\spewnotes from sphinxpackagefootnote (for sphinxheavybox)
%
% - \sphinxstylenotetitle, ..., \sphinxstylewarningtitle, etc... which are used by
%   default in the corresponding sphinx<notice> environments to replace at 6.2.0
%   formerly hard-coded \sphinxstrong{#1}<space>
%   Their definitions are in sphinxlatexstyletext.sty.


% Provides: (also in sphinxlatexliterals.sty)
\providecommand*\sphinxvspacefixafterfrenchlists{%
   \ifvmode\ifdim\lastskip<\z@ \vskip\parskip\fi\else\par\fi
}

% Some are quite plain
\newenvironment{sphinxseealso}[1]{\sphinxstyleseealsotitle{#1}}{}

% This \dimen register is a legacy relic from Sphinx 1.5 which is used now
% only for sphinxlightbox.  It is set in the sphinxadmonition environment.
\newdimen\spx@notice@border

\newenvironment{sphinxlightbox}{%
  \par
  \noindent{\color{spx@notice@bordercolor}%
            \rule{\linewidth}{\spx@notice@border}}%
  \par\nobreak
  {\parskip\z@skip\noindent}%
  }
  {%
  % counteract previous possible negative skip (French lists!):
  % (we can't cancel that any earlier \vskip introduced a potential pagebreak)
  \sphinxvspacefixafterfrenchlists
  \nobreak\vbox{\noindent\kern\@totalleftmargin
      {\color{spx@notice@bordercolor}%
       \rule[\dimexpr.4\baselineskip-\spx@notice@border\relax]
             {\linewidth}{\spx@notice@border}}\hss}\allowbreak
  }% end of sphinxlightbox environment definition

% note/hint/important/tip notices
%
% Since 1.5 these environments are named individually to allow user to
% redefine them entirely.
%
% The Sphinx definitions were done like this, prior to 6.2.0:
%
% \newenvironment{sphinxhint}[1]
%   {\begin{sphinxlightbox}\sphinxstrong{#1} }{\end{sphinxlightbox}}
%
% The more complex definition below will branch to sphinxheavybox if a certain
% boolean associated to the notice type is true.  This boolean is set to true
% whenever a CSS-named alike options for the notice type has been used in
% sphinxsetup.  The old coding as above would still work, with the new options
% being then simply ignored.  A user redefinition will probably either use
% directly sphinxlightbox or sphinxheavybox or something else, with no need to
% test the boolean.
%
% 6.2.0 also adds one layer of mark-up via \sphinxnotetitle etc..., because
% the former \sphinxstrong{#1}<space> used a too generic \sphinxstrong.  But
% perhaps the #1 should be passed over to sphinx{light,heavy}box as parameter.
% Unfortunately replacing these environments with one-parameter environments
% would be potentially a breaking change.  Anyway, sphinxpackageboxes.sty does not
% provide a "titled" box; the caption of code-blocks is handled by extra
% code in sphinxVerbatim.
\newenvironment{sphinxnote}[1]
  {\edef\spx@env{sphinx\ifspx@opt@heavynote heavy\else light\fi box}%
   \expandafter\begin\expandafter{\spx@env}\sphinxstylenotetitle{#1}}
  {\expandafter\end\expandafter{\spx@env}}
\newenvironment{sphinxhint}[1]
  {\edef\spx@env{sphinx\ifspx@opt@heavyhint heavy\else light\fi box}%
   \expandafter\begin\expandafter{\spx@env}\sphinxstylehinttitle{#1}}
  {\expandafter\end\expandafter{\spx@env}}
\newenvironment{sphinximportant}[1]
  {\edef\spx@env{sphinx\ifspx@opt@heavyimportant heavy\else light\fi box}%
   \expandafter\begin\expandafter{\spx@env}\sphinxstyleimportanttitle{#1}}
  {\expandafter\end\expandafter{\spx@env}}
\newenvironment{sphinxtip}[1]
  {\edef\spx@env{sphinx\ifspx@opt@heavytip heavy\else light\fi box}%
   \expandafter\begin\expandafter{\spx@env}\sphinxstyletiptitle{#1}}
  {\expandafter\end\expandafter{\spx@env}}

% warning/caution/attention/danger/error  get more distinction
%
% Code adapted from framed.sty's "snugshade" environment.
% Nesting works (inner frames do not allow page breaks).
\newenvironment{sphinxheavybox}{\par
   % 6.2.0 allows to not have to distinguish here between warning type notices
   % which always use sphinxheavybox or note type notices which might use it.
   % (MEMO: it is not a problem here if there is no sphinx<type>ShadowColor,
   %  as it used only if set)
   \spx@boxes@fcolorbox@setup{\spx@noticetype}%
   % Those are used by sphinxVerbatim if the \ifspx@inframed boolean is true
   \setlength{\FrameRule}{0.5\dimexpr\spx@boxes@border@top+\spx@boxes@border@bottom\relax}%
   % MEMO: prior to 5.1.0 \FrameSep was determined as 0.6\baselineskip -
   % \FrameRule, and there was no possibility for user to adjust padding.
   % Then \fcolorbox was used with \fboxrule set to \FrameRule and \fboxsep
   % set to \FrameSep.
   % The 5.1.0 default calculation of padding parameters maintains PDF output
   % identical to legacy behaviour, as long as padding is not set by user.
   \setlength{\FrameSep}{0.5\dimexpr\spx@boxes@padding@top+\spx@boxes@padding@bottom\relax}%
   % "setup" macro has prepared the \spx@boxes@... dimen registers
   \advance\spx@image@maxheight
   -\dimexpr\spx@boxes@border@top+\spx@boxes@border@bottom
           +\spx@boxes@padding@top+\spx@boxes@padding@bottom
           +\baselineskip\relax % will happen again if nested, needed indeed!
   % MEMO: the next comment is before boxing was extended to allow padding and
   % multiple border-widths, not to mention shadows...
   % configure framed.sty's parameters to obtain same vertical spacing
   % as for "light" boxes. We need for this to manually insert parskip glue and
   % revert a skip done by framed before the frame.
    \ltx@ifundefined{OuterFrameSep}{}{\OuterFrameSep\z@skip}%
    \vspace{\FrameHeightAdjust}
   % copied/adapted from framed.sty's snugshade
   % but now using in place of \fcolorbox the Sphinx sophisticated own
   \def\FrameCommand##1{%
       \hskip\@totalleftmargin
       % "setup" macro MUST have been called before
       \spx@boxes@fcolorbox{##1}%
       \hskip-\linewidth \hskip-\@totalleftmargin \hskip\columnwidth
   }%
   % 6.2.0 adds support for div.<notice type>_box-decoration-break=slice.
   % (it is yet undecided if slice style should inhibit a bottom shadow)
   \csname ifspx@\spx@noticetype @border@open\endcsname
     \def\FirstFrameCommand
               {\spx@boxes@fcolorbox@setup@openbottom\FrameCommand}%
     \def\MidFrameCommand
               {\spx@boxes@fcolorbox@setup@openboth  \FrameCommand}%
     \def\LastFrameCommand
               {\spx@boxes@fcolorbox@setup@opentop   \FrameCommand}%
   \fi
   \savenotes
   % use a minipage if we are already inside a framed environment
   \ifspx@inframed
       \noindent\begin{minipage}{\linewidth}
   \else
       % handle case where notice is first thing in a list item (or is quoted)
       \if@inlabel
        \noindent\par\vspace{-\baselineskip}
       \else
        \vspace{\parskip}
       \fi
   \fi
   \MakeFramed {\spx@inframedtrue
     \advance\hsize-\width \@totalleftmargin\z@ \linewidth\hsize
     % minipage initialization copied from LaTeX source code.
     \@pboxswfalse
     \let\@listdepth\@mplistdepth \@mplistdepth\z@
     \@minipagerestore
     \@setminipage }%
   \color@begingroup % workaround to an upstream framed.sty bug
   }
   {%
   \par\unskip
   \color@endgroup   % matches the \color@begingroup
   \@minipagefalse
   \endMakeFramed
   \ifspx@inframed\end{minipage}\fi
   % set footnotes at bottom of page
   \spewnotes
   % arrange for similar spacing below frame as for "light" boxes.
   \vskip .4\baselineskip
   }% end of sphinxheavybox environment definition

% - Since 1.5 these environments are named individually to allow user to
% redefine them entirely.
%
% - Since 5.1.0, sphinxheavybox is more versatile and four border widths, four
% padding widths, four corner radii, optional shadow, and three colors can all
% be modified via CSS-named alike options.
%
% - Since 6.2.0, also note/hint/important/tip notices can use these options
% and then they go automatically via sphinxheavybox.  If only the legacy options
% are used, they keep using sphinxlightbox.
%
% - Since 6.2.0, \sphinxwarningtitle etc... add one level of mark-up (they
% expand to \sphinxstrong{#1}<space> which was former hard-coded mark-up).
% Example:
% \renewcommand{\sphinxwarningtitle}[1]{\textbf{#1}\par\smallskip
%   {\color{sphinxwarningBorderColor}\hrule height1pt}\smallskip}
\newenvironment{sphinxwarning}[1]
  {\begin{sphinxheavybox}\sphinxstylewarningtitle{#1}}{\end{sphinxheavybox}}
\newenvironment{sphinxcaution}[1]
  {\begin{sphinxheavybox}\sphinxstylecautiontitle{#1}}{\end{sphinxheavybox}}
\newenvironment{sphinxattention}[1]
  {\begin{sphinxheavybox}\sphinxstyleattentiontitle{#1}}{\end{sphinxheavybox}}
\newenvironment{sphinxdanger}[1]
  {\begin{sphinxheavybox}\sphinxstyledangertitle{#1}}{\end{sphinxheavybox}}
\newenvironment{sphinxerror}[1]
  {\begin{sphinxheavybox}\sphinxstyleerrortitle{#1}}{\end{sphinxheavybox}}

% the main dispatch for all types of notices
\newenvironment{sphinxadmonition}[2]{% #1=type, #2=heading
  % can't use #1 directly in definition of end part
  \def\spx@noticetype {#1}%
  % those next three are a remnant of legacy code; they are not used at
  % all by sphinxheavybox, and their usage could be disposed of by sphinxlightbox
  % but we keep for backward compatibility and also because it may be simpler
  % for user redefinitions to employ for example "spx@notice@bgcolor" and not
  % the more bulky "sphinx\spx@noticetype BgColor".
  \sphinxcolorlet{spx@notice@bordercolor}{sphinx#1BorderColor}%
  \sphinxcolorlet{spx@notice@bgcolor}{sphinx#1BgColor}%
  \spx@notice@border \dimexpr\csname spx@#1@border\endcsname\relax
  % trigger the sphinx<type> environment, #2=heading is passed as argument
  \begin{sphinx#1}{#2}%
  % 6.2.0 support of div.<type>_TeX{color,extras} options
  \csname ifspx@\spx@noticetype @withtextcolor\endcsname
         \color{sphinx\spx@noticetype TextColor}%
  \fi
  \csname spx@\spx@noticetype @TeXextras\endcsname
  }
  % workaround some LaTeX "feature" of \end command (can't use "sphinx#1" here)
  {\edef\spx@temp{\noexpand\end{sphinx\spx@noticetype}}\spx@temp}

\endinput
