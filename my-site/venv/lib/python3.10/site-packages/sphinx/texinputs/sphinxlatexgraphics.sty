%% GRAPHICS
%
% change this info string if making any custom modification
\ProvidesFile{sphinxlatexgraphics.sty}[2021/01/27 graphics]

% Provides support for this output mark-up from Sphinx latex writer:
%
% - macros:
%
%   - \sphinxfigcaption
%   - \sphinxincludegraphics
%
% - environments:
%
%   - sphinxfigure-in-table
%
% May change:
%
% - \sphinxcaption (at begin document)
%
% Also provides:
%
% - \sphinxsafeincludegraphics (default of \sphinxincludegraphics since 2.0)
% - \spx@image@maxheight dimension (used by sphinxlatexadmonitions.sty)
% - \spx@image@box scratch box register (also used by sphinxlatexliterals.sty)
%
% Requires:
% \RequirePackage{graphicx}% done in sphinx.sty
\RequirePackage{amstext}% needed for \firstchoice@true(false)

% \sphinxincludegraphics resizes images larger than the TeX \linewidth (which
% is adjusted in indented environments), or taller than a certain maximal
% height (usually \textheight and this is reduced in the environments which use
% framed.sty to avoid infinite loop if image too tall).
%
% In case height or width options are present the rescaling is done
% (since 2.0), in a way keeping the width:height ratio either native from
% image or from the width and height options if both were present.
%
\newdimen\spx@image@maxheight
\AtBeginDocument{\spx@image@maxheight\textheight}

% box scratch register
\newbox\spx@image@box
\newcommand*{\sphinxsafeincludegraphics}[2][]{%
    % #1 contains possibly width=, height=, but no scale= since 1.8.4
    \setbox\spx@image@box\hbox{\includegraphics[#1,draft]{#2}}%
    \in@false % use some handy boolean flag
    \ifdim \wd\spx@image@box>\linewidth
      \in@true % flag to remember to adjust options and set box dimensions
      % compute height which results from rescaling width to \linewidth
      % and keep current aspect ratio. multiply-divide in \numexpr uses
      % temporarily doubled precision, hence no overflow. (of course we
      % assume \ht is not a few sp's below \maxdimen...(about 16384pt).
      \edef\spx@image@rescaledheight % with sp units
           {\the\numexpr\ht\spx@image@box
                        *\linewidth/\wd\spx@image@box sp}%
      \ifdim\spx@image@rescaledheight>\spx@image@maxheight
        % the rescaled height will be too big, so it is height which decides
        % the rescaling factor
        \def\spx@image@requiredheight{\spx@image@maxheight}% dimen register
        \edef\spx@image@requiredwidth % with sp units
         {\the\numexpr\wd\spx@image@box
                      *\spx@image@maxheight/\ht\spx@image@box sp}%
        % TODO: decide if this commented-out block could be needed due to
        % rounding in numexpr operations going up
        % \ifdim\spx@image@requiredwidth>\linewidth
        %     \def\spx@image@requiredwidth{\linewidth}% dimen register
        % \fi
      \else
        \def\spx@image@requiredwidth{\linewidth}% dimen register
        \let\spx@image@requiredheight\spx@image@rescaledheight% sp units
      \fi
    \else
      % width is ok, let's check height
      \ifdim\ht\spx@image@box>\spx@image@maxheight
        \in@true
        \edef\spx@image@requiredwidth % with sp units
            {\the\numexpr\wd\spx@image@box
                         *\spx@image@maxheight/\ht\spx@image@box sp}%
        \def\spx@image@requiredheight{\spx@image@maxheight}% dimen register
      \fi
    \fi % end of check of width and height
    \ifin@
      \setbox\spx@image@box
      \hbox{\includegraphics
            [%#1,% contained only width and/or height and overruled anyhow
            width=\spx@image@requiredwidth,height=\spx@image@requiredheight]%
            {#2}}%
      % \includegraphics does not set box dimensions to the exactly
      % requested ones, see https://github.com/latex3/latex2e/issues/112
      \wd\spx@image@box\spx@image@requiredwidth
      \ht\spx@image@box\spx@image@requiredheight
      \leavevmode\box\spx@image@box
    \else
      % here we do not modify the options, no need to adjust width and height
      % on output, they will be computed exactly as with "draft" option
      \setbox\spx@image@box\box\voidb@x % clear memory
      \includegraphics[#1]{#2}%
    \fi
}%
% Use the "safe" one by default (2.0)
\def\sphinxincludegraphics{\sphinxsafeincludegraphics}


%% FIGURE IN TABLE
%
\newenvironment{sphinxfigure-in-table}[1][\linewidth]{%
  \def\@captype{figure}%
  \sphinxsetvskipsforfigintablecaption
  \begin{minipage}{#1}%
}{\end{minipage}}
% tabulary expands twice contents, we need to prevent double counter stepping
\newcommand*\sphinxfigcaption
  {\ifx\equation$%$% this is trick to identify tabulary first pass
       \firstchoice@false\else\firstchoice@true\fi
   \spx@originalcaption }
\newcommand*\sphinxsetvskipsforfigintablecaption
  {\abovecaptionskip\smallskipamount
   \belowcaptionskip\smallskipamount}

\endinput
